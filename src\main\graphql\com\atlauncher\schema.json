{"__schema": {"queryType": {"name": "Query"}, "mutationType": {"name": "Mutation"}, "subscriptionType": null, "types": [{"kind": "OBJECT", "name": "About", "description": "Information about the launcher and it's contributors.", "fields": [{"name": "contributors", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Contributor", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Action", "description": "An action to run after the install is complete.", "fields": [{"name": "action", "description": "The action to run.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "ActionAction", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "after", "description": "What to do with the original file after running the action.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "client", "description": "If this action should be run for client side use.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mod", "description": "A list of `name` attributes of mods to run this action on. For `createzip` action, this is a list of mods that will be extracted and combined into a single zip. For `rename` action this will be a singular mod (in a list) to rename.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "saveAs", "description": "The filename to save the output of the action to.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "server", "description": "If this action should be run for server side use.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "When `action` is `createzip`, this is the type of the resulting file and where it should be placed.", "args": [], "type": {"kind": "ENUM", "name": "ActionType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ActionAction", "description": "The decomp type of this mod and where the decompressed file should be placed.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATE_ZIP", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "RENAME", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "ActionType", "description": "The decomp type of this mod and where the decompressed file should be placed.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "COREMODS", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "JAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MODS", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AddLauncherLaunchInput", "description": null, "fields": null, "inputFields": [{"name": "hash", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "installMethod", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "LauncherInstallMethod", "ofType": null}}, "defaultValue": null}, {"name": "javaVersion", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "LauncherJavaVersionInput", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AddPackActionInput", "description": null, "fields": null, "inputFields": [{"name": "action", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "PackLogAction", "ofType": null}}, "defaultValue": null}, {"name": "packId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "ID", "description": "The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `\"4\"`) or integer (such as `4`) input value will be accepted as an ID.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AddPackTimePlayedInput", "description": null, "fields": null, "inputFields": [{"name": "packId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null}, {"name": "time", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Configs", "description": "The information about the configs bundle. This can be downloaded from `${CDN_URL}/packs/${PACK_NAME}/versions/${PACK_VERSION}/Configs.zip`", "fields": [{"name": "filesize", "description": "The filesize of the configs bundle.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sha1", "description": "The SHA1 hash of the configs bundle.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Contributor", "description": "A contributor to the launcher.", "fields": [{"name": "avatarUrl", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "contributions", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "DateTime", "description": "A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "DecompType", "description": "The decomp type of this mod and where the decompressed file should be placed.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "COREMODS", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "JAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MODS", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ROOT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Delete", "description": "A file/folder to be deleted.", "fields": [{"name": "base", "description": "The base directory used for deleting files/folders.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "DeleteBase", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "target", "description": "The file/folder within the base path to delete.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "DeleteBase", "description": "The base directory used for deleting files/folders.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ROOT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Deletes", "description": "A list of files/folders that should be deleted when updating to this version.", "fields": [{"name": "files", "description": "A list of files to be deleted.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Delete", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "folders", "description": "A list of folders to be deleted.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Delete", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "Download", "description": "The download type of this mod and how the `url` property should be downloaded.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "BROWSER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DIRECT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "SERVER", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "ExtraArguments", "description": "Any extra arguments that should be used when launching a client instance.", "fields": [{"name": "arguments", "description": "The argumenrs to apply when launching a client instance.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "depends", "description": "If this is set, it requires that a mod with the given name be selected for install (either by the user or is defaulted to be installed). If it was not selected, this arguments should not apply.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dependsGroup", "description": "If this is set, it requires that any mod with the given group be selected for install (either by the user or is defaulted to be installed). If none were selected, this arguments should not apply.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ExtractTo", "description": "Where a mod archive should be extracted to.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ROOT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "FabricLoaderVersion", "description": "A version of the Fabric modloader.", "fields": [{"name": "build", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "clientJson", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverJson", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "stable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "FabricLoaderVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "FabricLoaderVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "FabricLoaderVersionsOrderByField", "ofType": null}, "defaultValue": "CREATED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "FabricLoaderVersionsOrderByDirection", "description": "The direction in which to sort the results by.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "FabricLoaderVersionsOrderByField", "description": "How to order the results.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "ForgeVersion", "description": "A version of the Forge modloader.", "fields": [{"name": "clientSha1Hash", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "clientSize", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "installerSha1Hash", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "installerSize", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraft", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "rawVersion", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "recommended", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverSha1Hash", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverSize", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "universalSha1Hash", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "universalSize", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ForgeVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "ForgeVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "ForgeVersionsOrderByField", "ofType": null}, "defaultValue": "CREATED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ForgeVersionsOrderByDirection", "description": "The direction in which to sort the results by.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "ForgeVersionsOrderByField", "description": "How to order the results.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Java", "description": "The version of Java needed to run.", "fields": [{"name": "max", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "JavaRuntime", "description": "A version of Minecraft.", "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "os", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "JavaRuntimeOS", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sha1Hash", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "size", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "JavaRuntimeOS", "description": "The OS for a Java Runtime.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "linux", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "linux_arm", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "linux_arm64", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "linux_i386", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mac_os", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mac_os_arm64", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "windows_arm64", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "windows_x64", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "windows_x86", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "KeepFiles", "description": "A file to be saved between updates.", "fields": [{"name": "base", "description": "The base directory used for saving files.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "KeepFilesBase", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "target", "description": "The file within the base path to keep.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "KeepFilesBase", "description": "The base directory used for saving files between updates.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CONFIG", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ROOT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "LauncherInstallMethod", "description": "The install method of the launcher.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "LINUX_AUR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_AUR_BIN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_DEB", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_FLATPAK", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_JAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_RPM", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_SOURCE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LINUX_UNKNOWN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MAC_APP", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MAC_JAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MAC_SOURCE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MAC_UNKNOWN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WINDOWS_JAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WINDOWS_PORTABLE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WINDOWS_SETUP", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WINDOWS_SOURCE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WINDOWS_UNKNOWN", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "LauncherJavaVersionInput", "description": null, "fields": null, "inputFields": [{"name": "bitness", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "majorVersion", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "raw", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "usingJreDir", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LauncherModPackResult", "description": "The definition for a modpack result.", "fields": [{"name": "iconUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "platform", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "ModPackPlatformType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sort", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "summary", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LegacyFabricLoaderVersion", "description": "A version of the LegacyFabric modloader.", "fields": [{"name": "build", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "clientJson", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverJson", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "stable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "LegacyFabricLoaderVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "LegacyFabricLoaderVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "LegacyFabricLoaderVersionsOrderByField", "ofType": null}, "defaultValue": "CREATED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "LegacyFabricLoaderVersionsOrderByDirection", "description": "The direction in which to sort the results by.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "LegacyFabricLoaderVersionsOrderByField", "description": "How to order the results.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Library", "description": "A library to be installed and loaded into the classpath when launching an instance.", "fields": [{"name": "depends", "description": "If this is set, it requires that a mod with the given name be selected for install (either by the user or is defaulted to be installed). If it was not selected, this library should not be downloaded.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dependsGroup", "description": "If this is set, it requires that any mod with the given group be selected for install (either by the user or is defaulted to be installed). If none were selected, this library should not be downloaded.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "download", "description": "The download type of this library and how the `url` property should be downloaded.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Download", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "file", "description": "The filename of this library. This can differ from the filename in the url.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "filesize", "description": "The filesize of the file.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "force", "description": "If this library should be forced to download regardless of if it already exists and has the correct hash.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "md5", "description": "The MD5 hash of the file.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "path", "description": "The path of the library that the file should be downloaded to and added to the classpath on the client side.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "server", "description": "The path of the library that the file should be downloaded to and added to the classpath on the client side.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "The url of this library. In the case of `download` property being `SERVER`, this **MAY** be a partial url which should be added to the end of a compatible ATLauncher cdn.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Loader", "description": "The definition for a mod loader.", "fields": [{"name": "choose", "description": "If the user can choose the version of the mod loader they want.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "className", "description": "The classname of the loader for use in ATLauncher.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metadata", "description": "Metadata for the mod loader.", "args": [], "type": {"kind": "OBJECT", "name": "LoaderMetadata", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "The type of mod loader to install and use.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LoaderMetadata", "description": "The definition for a pack version.", "fields": [{"name": "installerSha1", "description": "The SHA1 hash of the installer.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "installerSize", "description": "The filesize of the installer.", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "loader", "description": "The specific version of Fabric/Quilt loader to install.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraft", "description": "The Minecraft version this mod loader is for.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "rawVersion", "description": "The raw version of the mod loader to use. This is only used for Forge when they prefix the Minecraft versions onto some of their versions.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": "The version of the mod loader to use.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "yarn", "description": "The specific version of Yarn mappings to install.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LoaderVersions", "description": "The loaders and versions for a version of Minecraft.", "fields": [{"name": "fabric", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FabricLoaderVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "forge", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ForgeVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "legacyfabric", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LegacyFabricLoaderVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraftVersion", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "neoforge", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "NeoForgeVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paper", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PaperVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "purpur", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PurpurMCBuild", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "quilt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "QuiltLoaderVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "LoginInput", "description": null, "fields": null, "inputFields": [{"name": "email", "description": "Your email address", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "password", "description": "Your password", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "username", "description": "Your username", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LoginResult", "description": "The result of a login.", "fields": [{"name": "accessToken", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshToken", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MainClass", "description": "The main class used when launching an instance of this pack version. Shouldn't be used when combined with a `loader`.", "fields": [{"name": "depends", "description": "If this is set, it requires that a mod with the given name be selected for install (either by the user or is defaulted to be installed). If it was not selected, this main class should not apply.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dependsGroup", "description": "If this is set, it requires that any mod with the given group be selected for install (either by the user or is defaulted to be installed). If none were selected, this main class should not apply.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mainClass", "description": "The main class used when launching an instance of this pack version. Shouldn't be used when combined with a `loader`.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Messages", "description": "Messages that should be shown to the user when installing/updating.", "fields": [{"name": "install", "description": "The message to show at the start of an install.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "update", "description": "The message to show at the start of an update.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MinecraftVersion", "description": "A version of Minecraft.", "fields": [{"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasServer", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sha1Hash", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "size", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "MinecraftVersionType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "MinecraftVersionType", "description": "The type Minecraft version.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "experiment", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "old_alpha", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "old_beta", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "release", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "snapshot", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Mod", "description": "The definition for a mod.", "fields": [{"name": "authors", "description": "The authors of this mod.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "client", "description": "If this mod should be downloaded and installed for client side use.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "colour", "description": "The key of the colour in the root `colours` property that should be applied to this mod.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decompFile", "description": "The file that was within the decompressed archive that should be placed where `decompType` defines.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decompType", "description": "The decomp type of this mod and where the decompressed file should be placed.", "args": [], "type": {"kind": "ENUM", "name": "DecompType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "depends", "description": "The dependencies of this mod. If this mod is installed (either by default by being non optional, or being optional and selected by the user), then the list of names of mods in this property should also be installed.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": "The description of the mod.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "donation", "description": "The link to a donation page for the mod.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "download", "description": "The download type of this mod and how the `url` property should be downloaded.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Download", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "extractFolder", "description": "The folder inside the zip of this mod that should be extracted out (as set by `extractTo`).", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "extractTo", "description": "This defines that the mod download should be extracted to the location given. Can be optionally combined with `extractFolder` to extract a specific folder out of the archive.", "args": [], "type": {"kind": "ENUM", "name": "ExtractTo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "file", "description": "The filename of this mod. This can differ from the filename in the url. Note that `filePrefix`, if it is defined, should be prefixed to any final filename.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "filePrefix", "description": "A prefix that should be applied to the start of the `file` attribute.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "filesize", "description": "The filesize of the file.", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "force", "description": "If this mod should be forced to download regardless of if it already exists and has the correct hash.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "group", "description": "The group that this mod belongs to. Only one mod within a group can be selected.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hidden", "description": "If the mod should be hidden from view of the user installing it. Most commonly used with `depends` and `library` properties.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "library", "description": "If this mod is a library. These mods should only be installed if it's a dependant mod (meaning another mod which has this mods name in their `depends` property) and that mod was selected (or is default seleted or non optional).", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "linked", "description": "When an optional mod is selected/deselected, all mods that have this `linked` attribute set to the name of that mod should also be selected/deselected to match the linked mod.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "md5", "description": "The MD5 hash of the file.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "The name of this mod.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "optional", "description": "If this mod is optional for client side installs and the user must select it for it to be downloaded and used.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "recommended", "description": "If this mod is recommended to be installed. This applies only to optional mods.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "selected", "description": "If this mod should be selected by default if it is an optional mod.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "server", "description": "If this mod should be downloaded and installed for server side use.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverDownload", "description": "The download type for the server mod. Used only when `serverSeparate` is true.", "args": [], "type": {"kind": "ENUM", "name": "Download", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverFile", "description": "The filename of the server file. Used only when `serverSeparate` is true.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverMd5", "description": "The MD5 hash of the server file. Used only when `serverSeparate` is true.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverOptional", "description": "If this mod is optional for server side installs and the user must select it for it to be downloaded and used.", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverSeparate", "description": "If this mod has a separate download when building a server. If true then see `serverUrl`, `serverType`, `serverFile`, `serverDownload` and `serverMd5` properties", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverType", "description": "The type of server version of the mod. Used only when `serverSeparate` is true.", "args": [], "type": {"kind": "ENUM", "name": "Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverUrl", "description": "The url of server version of the mod. Used only when `serverSeparate` is true.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "The type of mod this is. This dictates how and where the mod file needs to be installed.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "The url of this mod. In the case of `download` property being `SERVER`, this **MAY** be a partial url which should be added to the end of a compatible ATLauncher cdn.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": "The version of this mod.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "warning", "description": "The key of the warning in the root `warnings` property that should be shown to the user (with a Yes/No option) when selecting this optional mod. The mod should be unselected if the user selects the No option.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "website", "description": "The link to the website for the mod.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ModPackPlatformType", "description": "Which platform the modpack is on.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ATLAUNCHER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "CURSEFORGE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FTB", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MODRINTH", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "TECHNIC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "fields": [{"name": "addLauncherLaunch", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AddLauncherLaunchInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "addPackAction", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AddPackActionInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "addPackTimePlayed", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AddPackTimePlayedInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "login", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "LoginInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoginResult", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshAccessToken", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "RefreshAccessTokenInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RefreshAccessTokenResult", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "NeoForgeVersion", "description": "A version of the NeoForge modloader.", "fields": [{"name": "createdAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraft", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "rawVersion", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "recommended", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "NeoForgeVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "NeoForgeVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "NeoForgeVersionsOrderByField", "ofType": null}, "defaultValue": "CREATED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "NeoForgeVersionsOrderByDirection", "description": "The direction in which to sort the results by.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "NeoForgeVersionsOrderByField", "description": "How to order the results.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "News", "description": "A news item.", "fields": [{"name": "content", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "NewsType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "NewsType", "description": "The type of news item (admin or general).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "admin", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "general", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Pack", "description": "A pack on the platform.", "fields": [{"name": "canCreateServers", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "canPublish", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "configSizeLimit", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "devVersion", "description": "Get a dev version for a pack", "args": [{"name": "name", "description": "The dev version of the pack", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "PackVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "devVersions", "description": "Development versions for this pack", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "PackDevVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PackVersion", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "devVersionsCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "discordInviteUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fileSizeLimit", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasDiscordImage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isEnabled", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isFeatured", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isLoggingEnabled", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isTest", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isTweetingUpdatesEnabled", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isTweetingUpdatesToBothAccountsEnabled", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "latestVersion", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "PackVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "multiplier", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "position", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "safeName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "showOnWebsite", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "showTwitchStreams", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "showYoutubeVideos", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "supportUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "PackType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "users", "description": "Users who admin this pack", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": "Get a version for a pack", "args": [{"name": "version", "description": "The version of the pack", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "PackVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "versions", "description": "Published versions for this pack", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "PackVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PackVersion", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "versionsCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "websiteUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "PackDevVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "PackDevVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "PackDevVersionsOrderByField", "ofType": null}, "defaultValue": "UPDATED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "PackDevVersionsOrderByDirection", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "PackDevVersionsOrderByField", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ID", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PUBLISHED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "UPDATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "PackInput", "description": "A pack, either by it's Id or it's safe name", "fields": null, "inputFields": [{"name": "id", "description": "The packs id", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "safeName", "description": "The packs safe name", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "PackLogAction", "description": "The status of a user.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "install", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "play", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "server", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "update", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "PackType", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "private", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "public", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "semipublic", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "PackVersion", "description": "A version for a pack.", "fields": [{"name": "canUpdate", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "changelog", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hash", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "healthStatus", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDevelopment", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRecommended", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "json", "description": "The pack definition", "args": [], "type": {"kind": "OBJECT", "name": "PackVersionJson", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraftVersion", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pack", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Pack", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "publishedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "raw<PERSON>son", "description": "The pack definition as a raw json string", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "PackVersionJson", "description": "The definition for a pack version.", "fields": [{"name": "actions", "description": "A list of actions that should be run after the installation is complete.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Action", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "caseAllFiles", "description": "If set, all downloaded mod files should be either all uppercased (if value is `upper`) or all lowercased (if value is `lower`).", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "colours", "description": "A map of colours for mods.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "configs", "description": "Details of the configuration archive for this pack.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Configs", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deletes", "description": "A list of files/folders that should be deleted when updating to this version.", "args": [], "type": {"kind": "OBJECT", "name": "Deletes", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "enableCurseIntegration", "description": "If an installed instance of this pack version should allow adding/reinstalling/updating mods from mod repositories (CurseForge or Modrinth).", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enableEditingMods", "description": "If an installed instance of this pack version should allow editing the mods.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "extraArguments", "description": "Any extra arguments that should be used when launching a client instance.", "args": [], "type": {"kind": "OBJECT", "name": "ExtraArguments", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "java", "description": "The version of Java needed to run.", "args": [], "type": {"kind": "OBJECT", "name": "Java", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "keep", "description": "A list of files that should be kept when updating to this version.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "KeepFiles", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "libraries", "description": "A list of libraries that should be downloaded and installed. This should not be used when `loader` is defined.", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Library", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "loader", "description": "The mod loader that should be installed.", "args": [], "type": {"kind": "OBJECT", "name": "Loader", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mainClass", "description": "The main class used when launching an instance of this pack version. Shouldn't be used when combined with a `loader`.", "args": [], "type": {"kind": "OBJECT", "name": "MainClass", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "memory", "description": "The recommended memory (in MB) that should be used when launching an instance of this pack version.", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "messages", "description": "Messages that should be shown to the user when installing/updating.", "args": [], "type": {"kind": "OBJECT", "name": "Messages", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraft", "description": "The Minecraft version.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mods", "description": "The mods to install.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Mod", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "noConfigs", "description": "If there are no config files.", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "permgen", "description": "The recommended permgen/metaspace (in MB) that should be used when launching an instance of this pack version.", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverArguments", "description": "Any extra arguments that should be used when launching a server instance.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": "The version.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "warnings", "description": "A map of warnings that should be shown to the user when mods with a `warning` property are selected.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "PackVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "PackVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "PackVersionsOrderByField", "ofType": null}, "defaultValue": "PUBLISHED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "PackVersionsOrderByDirection", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "PackVersionsOrderByField", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ID", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PUBLISHED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "UPDATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "PacksOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "PacksOrderByDirection", "ofType": null}, "defaultValue": "ASC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "PacksOrderByField", "ofType": null}, "defaultValue": "POSITION"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "PacksOrderByDirection", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "PacksOrderByField", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ID", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "POSITION", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "UPDATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "PaperVersion", "description": "A Paper Version.", "fields": [{"name": "build", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "downloadUrl", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "filename", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "promoted", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sha256", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "PurpurMCBuild", "description": "A Purpur Version.", "fields": [{"name": "build", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "PurpurVersion", "description": "A Purpur Version.", "fields": [{"name": "build", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "downloadUrl", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "filename", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "md5", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Query", "description": null, "fields": [{"name": "about", "description": "Get information about the launcher and it's contributors.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "About", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "adminNews", "description": "News items for the launcher", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "News", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "fabricLoaderVersion", "description": "Get a specific version of the Fabric modloader", "args": [{"name": "minecraftVersion", "description": "The Minecraft version", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "FabricLoaderVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fabricLoaderVersions", "description": "Versions of the Fabric modloader", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "FabricLoaderVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "FabricLoaderVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "forgeVersion", "description": "Get a specific version of the Forge modloader", "args": [{"name": "version", "description": "The version number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "ForgeVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "forgeVersions", "description": "Versions of the Forge modloader", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "minecraft", "description": "The Minecraft version to get Forge versions for.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "ForgeVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ForgeVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "generalNews", "description": "General news items", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "News", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "javaRuntimes", "description": "Java runtimes for the given OS.", "args": [{"name": "os", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "JavaRuntimeOS", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "JavaRuntime", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "legacyFabricLoaderVersion", "description": "Get a specific version of the Legacy Fabric modloader", "args": [{"name": "minecraftVersion", "description": "The Minecraft version", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "LegacyFabricLoaderVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "legacyFabricLoaderVersions", "description": "Versions of the Legacy Fabric modloader", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "LegacyFabricLoaderVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LegacyFabricLoaderVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "loaderVersions", "description": "Loader versions for a specific version of Minecraft", "args": [{"name": "minecraftVersion", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoaderVersions", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "me", "description": "Get your information as a logged in user", "args": [], "type": {"kind": "OBJECT", "name": "User", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraftVersion", "description": "Get a specific version of Minecraft.", "args": [{"name": "version", "description": "The Minecraft version.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "MinecraftVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "minecraftVersions", "description": "Versions of Minecraft.", "args": [{"name": "types", "description": "The types of Minecraft versions to get.", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "MinecraftVersionType", "ofType": null}}}, "defaultValue": "[release]"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MinecraftVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "neoForgeVersion", "description": "Get a specific version of the NeoForge modloader", "args": [{"name": "version", "description": "The version number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "NeoForgeVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "neoForgeVersions", "description": "Versions of the NeoForge modloader", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "minecraft", "description": "The Minecraft version to get NeoForge versions for.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "NeoForgeVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "NeoForgeVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pack", "description": "Get a pack in the system", "args": [{"name": "pack", "description": "The pack", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "PackInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Pack", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "packVersion", "description": "Get a version of a pack in the system", "args": [{"name": "pack", "description": "The pack", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "PackInput", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version in the pack", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "PackVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "packs", "description": "Packs in the system", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "PacksOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Pack", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paperVersion", "description": "Get a specific version of the Paper modloader", "args": [{"name": "build", "description": "The build number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "minecraft", "description": "The Minecraft version to get Paper versions for.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "PaperVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "paperVersions", "description": "Versions of the Paper modloader", "args": [{"name": "minecraft", "description": "The Minecraft version to get Paper versions for.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PaperVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "purpurVersion", "description": "Get a specific version of the Purpur modloader", "args": [{"name": "build", "description": "The build number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "minecraft", "description": "The Minecraft version to get Purpur versions for.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "PurpurVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "purpurVersions", "description": "Versions of the Purpur modloader", "args": [{"name": "minecraft", "description": "The Minecraft version to get Purpur versions for.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PurpurMCBuild", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "quiltLoaderVersion", "description": "Get a specific version of the Quilt modloader", "args": [{"name": "minecraftVersion", "description": "The Minecraft version", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "QuiltLoaderVersion", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "quiltLoaderVersions", "description": "Versions of the Quilt modloader", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "QuiltLoaderVersionsOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "QuiltLoaderVersion", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "rateLimit", "description": "Get information about your rate limiting as well as cost of your query.", "args": [], "type": {"kind": "OBJECT", "name": "RateLimit", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "searchPacks", "description": "Search for packs in the system", "args": [{"name": "field", "description": "The field to search in", "type": {"kind": "ENUM", "name": "SearchPacksField", "ofType": null}, "defaultValue": "NAME"}, {"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "PacksOrderBy", "ofType": null}, "defaultValue": null}, {"name": "query", "description": "The query to search for", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Pack", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "servers", "description": "Minecraft servers in the system", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Server", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serversForPack", "description": "Servers for a given pack", "args": [{"name": "packId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "platform", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "ServerPackPlatform", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Server", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "unifiedModPackHome", "description": "Get featured packs across multiple platforms", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LauncherModPackResult", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "unifiedModPackSearch", "description": "Search for modpacks across multiple platforms", "args": [{"name": "query", "description": "The query to search for", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LauncherModPackResult", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "users", "description": "Users in the system. Only accessible to platform admins.", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "UsersOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "QuiltLoaderVersion", "description": "A version of the Quilt modloader.", "fields": [{"name": "build", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "clientJson", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serverJson", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "stable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "QuiltLoaderVersionsOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "QuiltLoaderVersionsOrderByDirection", "ofType": null}, "defaultValue": "DESC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "QuiltLoaderVersionsOrderByField", "ofType": null}, "defaultValue": "CREATED_AT"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "QuiltLoaderVersionsOrderByDirection", "description": "The direction in which to sort the results by.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "QuiltLoaderVersionsOrderByField", "description": "How to order the results.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "CREATED_AT", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "RateLimit", "description": "The rate limit information.", "fields": [{"name": "cost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "limit", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryLimit", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "remaining", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "resetsIn", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "RefreshAccessTokenInput", "description": null, "fields": null, "inputFields": [{"name": "refreshToken", "description": "The refresh token", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "RefreshAccessTokenResult", "description": "The result of a refreshAccessToken call.", "fields": [{"name": "accessToken", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshToken", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "SearchPacksField", "description": "Which field to search on.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "DESCRIPTION", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "NAME", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Server", "description": "A Minecraft server.", "fields": [{"name": "bannerUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "host", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isFeatured", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOnline", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "language", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "motd", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "onlinePlayers", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "packs", "description": "The packs that can connect to this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ServerPack", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "port", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "position", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "safeName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "shortDescription", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "shouldShowOnLauncher", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "websiteUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ServerPack", "description": "A pack that can connect to a server.", "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "platform", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "ServerPackPlatform", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ServerPackPlatform", "description": "The platform of the pack.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ATLAUNCHER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "CURSEFORGE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MODRINTH", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "VANILLA", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "Type", "description": "The type of mod this is. This dictates how and where the mod file needs to be installed.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "DEPENDENCY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "EXTRACT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FORGE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MODS", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "RESOURCEPACK", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "User", "description": "A user on the platform.", "fields": [{"name": "apiToken", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "discordUserId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "email", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mustChangePassword", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "newsLastRead", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "packs", "description": "Packs this user is an admin of", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "PacksOrderBy", "ofType": null}, "defaultValue": null}, {"name": "skip", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "0"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Pack", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "settings", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "UserSettings", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "UserStatus", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "topAdmin", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "username", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "UserSettings", "description": "A users settings.", "fields": [{"name": "collapseMenu", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "editor<PERSON><PERSON><PERSON>", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "muteAudio", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "UserStatus", "description": "The status of a user.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "active", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "banned", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UsersOrderBy", "description": "", "fields": null, "inputFields": [{"name": "direction", "description": "The direction to order by", "type": {"kind": "ENUM", "name": "UsersOrderByDirection", "ofType": null}, "defaultValue": "ASC"}, {"name": "field", "description": "The field to order by", "type": {"kind": "ENUM", "name": "UsersOrderByField", "ofType": null}, "defaultValue": "ID"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "UsersOrderByDirection", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "UsersOrderByField", "description": "The type of pack (public or private).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ID", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByUrl`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "locations": ["FIELD_DEFINITION", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "ENUM_VALUE"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\""}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behaviour of this scalar.", "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behaviour of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}]}]}}