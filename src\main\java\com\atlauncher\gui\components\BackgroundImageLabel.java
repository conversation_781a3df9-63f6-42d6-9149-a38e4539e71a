/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.gui.components;

import java.util.Optional;

import javax.swing.BorderFactory;
import javax.swing.JLabel;

import com.atlauncher.utils.Utils;
import com.atlauncher.workers.BackgroundImageWorker;

public final class BackgroundImageLabel extends JLabel {
    public BackgroundImageLabel(String url, int width, int height) {
        setIcon(Utils.getIconImage("/assets/image/no-icon.png"));
        setBorder(BorderFactory.createEmptyBorder(0, 5, 0, 5));
        setVisible(false);

        if (Optional.ofNullable(url).isPresent()) {
            new BackgroundImageWorker(this, url, width, height).execute();
        } else {
            setVisible(true);
        }
    }
}
