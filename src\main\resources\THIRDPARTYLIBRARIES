<h2>Build Dependencies</h2>
com.github.RyanTheAllmighty.gettext:gettext-gradle-plugin version aab5c30bf8
com.github.ATLauncher.gradle-macappbundle:edu.sc.seis.macAppBundle version d22f8cdb94
org.cadixdev.licenser:org.cadixdev.licenser version 0.6.1
com.adarshr.test-logger:com.adarshr.test-logger version 3.2.0
edu.sc.seis.macAppBundle:edu.sc.seis.macAppBundle version 2.3.0
edu.sc.seis.launch4j:edu.sc.seis.launch4j version 3.0.3
de.undercouch.download:de.undercouch.download version 5.4.0
com.github.johnrengelman.shadow:com.github.johnrengelman.shadow version 8.1.1
com.github.ben-manes.versions:com.github.ben-manes.versions version 0.47.0
com.apollographql.apollo:com.apollographql.apollo version 2.5.14
<h2>Application Dependencies</h2>
net.java.dev.jna:jna version 5.13.0
net.java.dev.jna:jna-platform version 5.13.0
com.google.code.gson:gson version 2.10.1
com.google.guava:guava version 32.1.1-jre
org.tukaani:xz version 1.9
com.mojang:authlib version 1.5.21
net.iharder:base64 version 2.3.9
com.github.Vatuu:discord-rpc version 1.6.2
net.sf.jopt-simple:jopt-simple version 5.0.4
org.zeroturnaround:zt-zip version 1.15
com.squareup.okhttp3:okhttp version 4.11.0
com.squareup.okhttp3:okhttp-tls version 4.11.0
io.sentry:sentry version 6.25.0
com.github.RyanTheAllmighty.gettext:gettext-lib version 88ae68d897
org.apache.logging.log4j:log4j-api version 2.20.0
org.apache.logging.log4j:log4j-core version 2.20.0
com.sangupta:murmur version 1.0.0
org.apache.commons:commons-lang3 version 3.12.0
org.apache.commons:commons-text version 1.10.0
org.apache.commons:commons-compress version 1.23.0
com.formdev:flatlaf version 3.1.1
com.formdev:flatlaf-extras version 3.1.1
com.github.oshi:oshi-core version 6.4.4
net.freeutils:jlhttp version 2.6
joda-time:joda-time version 2.12.5
org.commonmark:commonmark version 0.21.0
com.github.hypfvieh:dbus-java version 3.3.2
com.apollographql.apollo:apollo-runtime version 2.5.14
com.apollographql.apollo:apollo-http-cache version 2.5.14
com.apollographql.apollo:apollo-rx3-support version 2.5.14
com.github.MCRcortex:nekodetector version Version-1.1-pre
com.twelvemonkeys.imageio:imageio-webp version 3.12.0
io.reactivex.rxjava3:rxjava version 3.1.6
com.gitlab.doomsdayrs:rxswing version a5749ad421
