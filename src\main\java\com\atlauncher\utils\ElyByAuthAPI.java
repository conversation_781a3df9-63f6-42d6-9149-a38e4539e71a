/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.utils;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

import com.atlauncher.Gsons;
import com.atlauncher.constants.Constants;
import com.atlauncher.data.elyby.ElyByOAuthTokenResponse;
import com.atlauncher.data.elyby.ElyByProfile;
import com.atlauncher.network.DownloadException;
import com.atlauncher.network.NetworkClient;

import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * Various utility methods for interacting with the Ely.by Auth API.
 */
public class ElyByAuthAPI {
    
    public static String getOAuthAuthorizeUrl(String state) {
        return Constants.ELYBY_OAUTH_AUTHORIZE_URL + 
               "?client_id=" + Constants.ELYBY_CLIENT_ID +
               "&redirect_uri=" + Constants.ELYBY_LOGIN_REDIRECT_URL_ENCODED +
               "&response_type=code" +
               "&scope=" + String.join("%20", Constants.ELYBY_LOGIN_SCOPES) +
               "&state=" + state;
    }

    public static ElyByOAuthTokenResponse tradeCodeForAccessToken(String code) {
        RequestBody data = new FormBody.Builder()
            .add("client_id", Constants.ELYBY_CLIENT_ID)
            .add("redirect_uri", Constants.ELYBY_LOGIN_REDIRECT_URL)
            .add("grant_type", "authorization_code")
            .add("code", code)
            .build();

        ElyByOAuthTokenResponse tokenResponse = NetworkClient.post(Constants.ELYBY_OAUTH_TOKEN_URL,
            Headers.of("Content-Type", "application/x-www-form-urlencoded"), data, ElyByOAuthTokenResponse.class);

        return tokenResponse;
    }

    public static ElyByOAuthTokenResponse refreshAccessToken(String refreshToken) {
        RequestBody data = new FormBody.Builder()
            .add("client_id", Constants.ELYBY_CLIENT_ID)
            .add("grant_type", "refresh_token")
            .add("refresh_token", refreshToken)
            .add("scope", String.join(" ", Constants.ELYBY_LOGIN_SCOPES))
            .build();

        ElyByOAuthTokenResponse tokenResponse = NetworkClient.post(Constants.ELYBY_OAUTH_TOKEN_URL,
            Headers.of("Content-Type", "application/x-www-form-urlencoded"), data, ElyByOAuthTokenResponse.class);

        return tokenResponse;
    }

    @Nullable
    public static ElyByProfile getProfile(String accessToken) throws DownloadException {
        return NetworkClient.getWithThrow(
            Constants.ELYBY_ACCOUNT_INFO_URL,
            Headers.of("Authorization", "Bearer " + accessToken),
            ElyByProfile.class);
    }

    public static boolean validateAccessToken(String accessToken) {
        try {
            Map<Object, Object> data = new HashMap<>();
            data.put("accessToken", accessToken);

            NetworkClient.post(Constants.ELYBY_AUTH_SERVER_URL + "/auth/validate",
                Headers.of("Content-Type", "application/json"),
                RequestBody.create(Gsons.DEFAULT.toJson(data), MediaType.get("application/json; charset=utf-8")),
                String.class);
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
