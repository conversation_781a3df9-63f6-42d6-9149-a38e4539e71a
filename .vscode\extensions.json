{
    // See http://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
    // Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
    // List of extensions which should be recommended for users of this workspace.
    "recommendations": [
        "vscjava.vscode-java-pack",
        "vscjava.vscode-java-dependency",
        "vscjava.vscode-java-test",
        "vscjava.vscode-java-debug",
        "redhat.java",
        "naco-siren.gradle-language",
        "rebornix.project-snippets",
        "mrorz.language-gettext",
        "aaron-bond.better-comments",
        "mathiasfrohlich.kotlin",
        "idleberg.innosetup"
    ]
}
