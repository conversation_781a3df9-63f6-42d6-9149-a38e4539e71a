name: Application

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  ci:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 1.8
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 8

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          validate-wrappers: true
          add-job-summary: never

      - name: Check license headers
        run: ./gradlew checkLicenses

  test:
    runs-on: ${{ matrix.os }}
    needs: ci
    timeout-minutes: 5
    strategy:
      matrix:
        java-version: ["8", "11", "17", "21", "23"] # LTS + Latest
        os: ["ubuntu-latest", "macos-latest", "windows-latest"]
    name: Java ${{ matrix.java-version }} (${{ matrix.os }}) Tests
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v4
        with:
          # MacOS (aarch64) Java 8 doesn't exist for temurin, so use zulu instead
          distribution: ${{ matrix.os =='macos-latest' && matrix.java-version == '8' && 'zulu' || 'temurin' }}
          java-version: ${{ matrix.java-version }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          validate-wrappers: true
          add-job-summary: never

      - name: Run Unit Tests
        run: ./gradlew --no-daemon test

      - name: Run UI Tests
        uses: GabrielBB/xvfb-action@v1
        continue-on-error: true
        with:
          run: ./gradlew --no-daemon uiTest

  build:
    runs-on: ${{ matrix.os }}
    timeout-minutes: 5
    needs: ci
    strategy:
      matrix:
        java-version: ["8", "11", "17", "21", "23"] # LTS + Latest
        os: ["ubuntu-latest", "macos-latest", "windows-latest"]
    name: Java ${{ matrix.java-version }} (${{ matrix.os }}) Build
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v4
        with:
          # MacOS (aarch64) Java 8 doesn't exist for temurin, so use zulu instead
          distribution: ${{ matrix.os =='macos-latest' && matrix.java-version == '8' && 'zulu' || 'temurin' }}
          java-version: ${{ matrix.java-version }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          validate-wrappers: true
          add-job-summary: never

      - name: Build
        run: ./gradlew build -x test

  package:
    runs-on: ubuntu-latest
    needs: ci
    timeout-minutes: 5
    outputs:
      version: ${{ steps.version.outputs.VERSION }}
      clean-version: ${{ steps.version.outputs.CLEAN_VERSION }}
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 17

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          validate-wrappers: true
          add-job-summary: never

      - name: Read version and remove stream
        id: version
        run: |
          echo "VERSION=$(cat ./src/main/resources/version | tr -d ' \t\n\r')" >> $GITHUB_OUTPUT
          echo "CLEAN_VERSION=$(grep -oE '[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' ./src/main/resources/version)" >> $GITHUB_OUTPUT

      - name: Build
        run: ./gradlew build -x test

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ATLauncher
          path: dist/*

  release:
    runs-on: ubuntu-latest
    needs: [test, build, package]
    timeout-minutes: 5
    if: ${{ github.ref == 'refs/heads/master' && !endsWith(needs.package.outputs.version, '.Beta') }}
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4

      - name: Download built artifacts
        uses: actions/download-artifact@v4

      - name: Get cleaned changelog text
        id: changelog
        run: |
          awk '/^### /{if (section) print section; section=$0 ORS; next} NF && section {section = section $0 ORS} END {if (section) print section}' CHANGELOG.md | awk '/^$/ {if (i) {b=b $0 "\n"} else {print $0 }; next} /^###/ {i=1; b=$0; next} {if (i) {print b}; i=0; print $0; next}' > fixed-changelog.md
          echo 'cleaned<<EOF' >> $GITHUB_OUTPUT
          cat fixed-changelog.md >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

      - name: Create ATLauncher Version
        uses: ATLauncher/release-atlauncher-action@master
        timeout-minutes: 30
        with:
          api-key: ${{ secrets.ATLAUNCHER_API_KEY }}
          version: ${{ needs.package.outputs.clean-version }}
          changelog: ${{ steps.changelog.outputs.cleaned }}
          files: |
            ./ATLauncher/ATLauncher-${{ needs.package.outputs.clean-version }}.exe
            ./ATLauncher/ATLauncher-${{ needs.package.outputs.clean-version }}.zip
            ./ATLauncher/ATLauncher-${{ needs.package.outputs.clean-version }}.jar

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag_name: v${{ needs.package.outputs.clean-version }}
          target_commitish: master
          name: ${{ needs.package.outputs.clean-version }}
          body: ${{ steps.changelog.outputs.cleaned }}
          prerelease: false
          draft: false
          files: |
            ./ATLauncher/ATLauncher-${{ needs.package.outputs.clean-version }}.exe
            ./ATLauncher/ATLauncher-${{ needs.package.outputs.clean-version }}.zip
            ./ATLauncher/ATLauncher-${{ needs.package.outputs.clean-version }}.jar

      - name: Create Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
        with:
          environment: production
          version: "${{ needs.package.outputs.clean-version }}"

      - name: Update version
        run: |
          NEW_VERSION=`echo ${{ needs.package.outputs.clean-version }} | perl -pe 's/^((\d+\.)*)(\d+)(.*)$/$1.($3+1).$4/e'`
          cat <<EOF > CHANGELOG.md
          # Changelog

          This changelog only contains the changes that are unreleased. For changes for individual releases, please visit the
          [releases](https://github.com/ATLauncher/ATLauncher/releases) page on GitHub.

          ## $NEW_VERSION

          ### New Features

          ### Fixes

          ### Misc
          EOF
          echo "${NEW_VERSION}.Beta" > src/main/resources/version

      - name: Commit new version/CHANGELOG file
        uses: EndBug/add-and-commit@v9
        with:
          add: "CHANGELOG.md src/main/resources/version"
          message: "chore: bump version"

      - name: Add comment, remove label and then close issues labeled with 'awaiting-release'
        uses: ./.github/actions/label-cleanup
        with:
          label: "awaiting-release"
          comment: "This has been fixed with version ${{ needs.package.outputs.clean-version }}"
          token: ${{ secrets.BOT_GITHUB_TOKEN }}

      - name: Package up AUR versions
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.BOT_GITHUB_TOKEN }}
          event-type: aur-publish
          client-payload: '{"version": "${{ needs.package.outputs.clean-version }}"}'
