/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.gui.dialogs;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

import javax.swing.JDialog;

import com.atlauncher.App;
import com.atlauncher.builders.HTMLBuilder;
import com.atlauncher.constants.Constants;
import com.atlauncher.data.ElyByAccount;
import com.atlauncher.data.elyby.ElyByOAuthTokenResponse;
import com.atlauncher.data.elyby.ElyByProfile;
import com.atlauncher.gui.panels.LoadingPanel;
import com.atlauncher.managers.AccountManager;
import com.atlauncher.managers.DialogManager;
import com.atlauncher.managers.LogManager;
import com.atlauncher.utils.ElyByAuthAPI;
import com.atlauncher.utils.OS;
import org.mini2Dx.gettext.GetText;

import net.freeutils.httpserver.HTTPServer;
import net.freeutils.httpserver.HTTPServer.VirtualHost;

@SuppressWarnings("serial")
public final class LoginWithElyByDialog extends JDialog {
    private static final HTTPServer server = new HTTPServer(Constants.ELYBY_LOGIN_REDIRECT_PORT);
    private static final VirtualHost host = server.getVirtualHost(null);
    public ElyByAccount account = null;
    private String loginMethod = "Unknown";
    private String state;

    public LoginWithElyByDialog() {
        this(null);
    }

    public LoginWithElyByDialog(ElyByAccount account) {
        super(App.launcher.getParent(), GetText.tr("Login with Ely.by"), ModalityType.DOCUMENT_MODAL);

        this.account = account;
        this.state = UUID.randomUUID().toString();
        this.setMinimumSize(new Dimension(500, 500));
        this.setResizable(false);
        this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);

        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent arg0) {
                close();
            }
        });

        if (setupWebServer()) {
            showLoginPage();
        } else {
            showError("Failed to start local server for OAuth callback");
        }
    }

    private boolean setupWebServer() {
        try {
            // Stop server if it's already running
            try {
                server.stop();
            } catch (Exception e) {
                // Ignore if server wasn't running
            }

            host.addContext("/", (req, res) -> {
                LogManager.info("Received OAuth callback: " + req.getURI().toString());

                if (req.getParams().containsKey("error")) {
                    res.getHeaders().add("Content-Type", "text/plain");
                    res.send(500, GetText.tr("Error logging in. Check console for more information"));
                    LogManager.error("Error logging into Ely.by account: " + req.getParams().get("error"));
                    if (req.getParams().containsKey("error_description")) {
                        LogManager.error("Error description: " + URLDecoder
                            .decode(req.getParams().get("error_description"), StandardCharsets.UTF_8.toString()));
                    }
                    close();
                    return 0;
                }

                if (!req.getParams().containsKey("code")) {
                    res.getHeaders().add("Content-Type", "text/plain");
                    res.send(400, GetText.tr("Code is missing"));
                    LogManager.error("OAuth callback missing authorization code");
                    close();
                    return 0;
                }

                String returnedState = req.getParams().get("state");
                if (!state.equals(returnedState)) {
                    res.getHeaders().add("Content-Type", "text/plain");
                    res.send(400, GetText.tr("Invalid state parameter"));
                    LogManager.error("OAuth state mismatch. Expected: " + state + ", Got: " + returnedState);
                    close();
                    return 0;
                }

                try {
                    LogManager.info("Processing OAuth authorization code...");
                    acquireAccessToken(req.getParams().get("code"));
                    LogManager.info("Successfully processed Ely.by login");
                } catch (Exception e) {
                    LogManager.logStackTrace("Error acquiring accessToken", e);
                    res.getHeaders().add("Content-Type", "text/html");
                    res.send(500, GetText.tr("Error logging in. Check console for more information"));
                    close();
                    return 0;
                }

                res.getHeaders().add("Content-Type", "text/plain");
                res.send(200, GetText.tr("Login complete. You can now close this window and go back to {0}",
                    Constants.LAUNCHER_NAME));
                close();
                return 0;
            }, "GET");

            server.start();
            LogManager.info("Started Ely.by OAuth callback server on port " + Constants.ELYBY_LOGIN_REDIRECT_PORT);
            return true;
        } catch (IOException e) {
            LogManager.logStackTrace("Error setting up web server for Ely.by login", e);
            return false;
        }
    }

    private void showLoginPage() {
        this.loginMethod = "Browser";

        LoadingPanel loadingPanel = new LoadingPanel(new HTMLBuilder().center().text(GetText.tr(
                "Please login to your Ely.by account in the browser window that opened.<br/><br/>If a browser window didn't open, please manually open the following URL:<br/><br/>{0}",
                ElyByAuthAPI.getOAuthAuthorizeUrl(state))).build());

        this.setContentPane(loadingPanel);
        this.setVisible(true);

        OS.openWebBrowser(ElyByAuthAPI.getOAuthAuthorizeUrl(state));
    }

    private void showError(String message) {
        DialogManager.okDialog().setTitle(GetText.tr("Login Failed"))
                .setContent(GetText.tr("Failed to login to Ely.by") + ":<br/><br/>" + message)
                .setType(DialogManager.ERROR).show();
    }

    private void close() {
        server.stop();
        setVisible(false);
        dispose();
    }

    private void addAccount(ElyByOAuthTokenResponse tokenResponse, ElyByProfile profile) throws Exception {
        if (account != null || AccountManager.isElyByAccountByName(profile.username)) {
            ElyByAccount existingAccount = AccountManager.getElyByAccountByName(profile.username);

            if (existingAccount == null) {
                return;
            }

            // if forced to relogin, then make sure they logged into correct account
            if (this.account != null && !existingAccount.username.equals(this.account.username)) {
                DialogManager.okDialog().setTitle(GetText.tr("Incorrect account"))
                    .setContent(
                        GetText.tr("Logged into incorrect account. Please login again on the Accounts tab."))
                    .setType(DialogManager.ERROR).show();
                return;
            }

            existingAccount.update(tokenResponse, profile);
            AccountManager.saveElyByAccounts();
        } else {
            ElyByAccount newAccount = new ElyByAccount(tokenResponse, profile);

            AccountManager.addElyByAccount(newAccount);
            this.account = newAccount;
        }
    }

    private void acquireAccessToken(String authcode) throws Exception {
        ElyByOAuthTokenResponse tokenResponse = ElyByAuthAPI.tradeCodeForAccessToken(authcode);

        if (tokenResponse == null) {
            throw new Exception("Failed to get access token");
        }

        ElyByProfile profile = ElyByAuthAPI.getProfile(tokenResponse.accessToken);

        if (profile == null) {
            throw new Exception("Failed to get profile information");
        }

        addAccount(tokenResponse, profile);
    }
}
