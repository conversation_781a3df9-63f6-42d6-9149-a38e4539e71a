{"useLwjglReplacement": false, "removeInitialMemoryOption": true, "useGraphqlLauncherLaunch": true, "outdatedJavaPrompt": {"enabled": true, "version": 1, "everyLaunch": false, "forced": false, "majorVersion": 17, "downloadLink": "https://adoptium.net/temurin/releases/?package=jre&version=17"}, "bundledJre": {"promptVersion": 2, "promptToUpdate": true, "majorVersion": 17, "version": "17.0.9", "windowsx64": {"url": "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9.1/OpenJDK17U-jre_x64_windows_hotspot_17.0.9_9.zip", "hash": "6c491d6f8c28c6f451f08110a30348696a04b009f8c58592191046e0fab1477b", "size": 43447242, "folder": "jdk-17.0.9+9-jre"}, "windowsx86": {"url": "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9.1/OpenJDK17U-jre_x86-32_windows_hotspot_17.0.9_9.zip", "hash": "2f9fe8b587400e89cd3ef33b71e0517ab99a12a5ee623382cbe9f5078bf2b435", "size": 40630044, "folder": "jdk-17.0.9+9-jre"}}, "movedPacksTest": [{"fromPack": "190", "toPack": "491", "fromVersion": "1.3.2", "toVersion": "4.2.4"}], "analytics": {"enabled": false, "enabledForAll": false, "enabledVersion": "********", "enabledNew": true, "percentage": 100, "autoSendInSeconds": 300}, "useGraphql": {"vanillaLoaderVersions": true, "loaderVersions": true, "loaderVersionsNonForge": true, "packActions": true, "news": false, "unifiedModPacks": true, "minecraftVersions": false, "javaRuntimes": false, "launcherLaunch": true}, "minecraft": {"experiment": {"enabled": true, "disabledVersions": []}, "snapshot": {"enabled": true, "disabledVersions": []}, "release": {"enabled": true, "disabledVersions": []}, "old_alpha": {"enabled": true, "disabledVersions": []}, "old_beta": {"enabled": true, "disabledVersions": []}}, "neoForgeServerStarterJar": {"enabled": true, "url": "https://github.com/neoforged/ServerStarterJar/releases/download/0.1.25/server.jar", "hash": "98f22f895076e19ff367595cc05ed85c065deca3255c8f6b15969b2cdf849c85", "size": 21865}, "loaders": {"fabric": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "forge": {"enabled": true, "disabledMinecraftVersions": ["1.20.3"], "disabledVersions": ["43.1.63", "49.0.0", "49.0.1", "49.0.2", "49.0.3"]}, "legacyfabric": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "neoforge": {"enabled": true, "disableInstallerHashChecking": true, "disabledMinecraftVersions": [], "disabledVersions": ["47.1.7", "20.6.84", "20.6.85", "20.6.87"], "forgeCompatibleMinecraftVersions": ["1.20.1"]}, "paper": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "purpur": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "quilt": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": ["0.17.5-beta.4"], "switchHashedForIntermediary": false}}, "platforms": {"curseforge": {"messageddd": "CurseForge API is having issues currently and may sometimes show no versions available when installing modpacks or mods. There is nothing we can do about this, we've made CurseForge aware of the issue. You can download Packs/Mods from Modrinth or try again later.", "modsEnabled": true, "modpacksEnabled": true}, "ftb": {"modpacksEnabled": true}, "modpacksch": {"modpacksEnabled": false}, "modrinth": {"ddmessage": "Modrinth are currently having issues which are causing mod/modpack searches to not work. Try again later.", "modsEnabled": true, "modpacksEnabled": true}, "technic": {"modpacksEnabled": true}}, "errorReporting": {"enabled": true, "ignoredMessages": ["Network is unreachable: connect", "Permission denied: connect", "Connection timed out: connect", "Connection refused: connect", "failed to delete", "failed to rename", "Failed to connect to", "Connection reset", "timeout", "Read timed out", "Access is denied", "request wasn't successful", "There is not enough space on the disk", "The system cannot find the file specified", "being used by another process", "The cloud file provider is not running", "Name or service not known", "Received fatal alert: handshake_failure", "not verified (no certificates)", "No such host is known", "nodename nor servname provided, or not known", "NoSuchFileException", "Account does not own Minecraft", "Minecraft Profile not found", "UnknownHostException", "download.nodecdn.net", "Installation cancelled from browser downloads dialog", "response code: 400 for URL: https://api.atlauncher.com", "response code: 403 for URL: https://api.atlauncher.com", "The system cannot find the path specified", "HTTP 403", "Failed to find loader version"]}, "discordLinkMatching": {"curseForgeProjectIdsToDiscordLink": {"285109": "https://discord.com/invite/shivaxi", "252507": "https://discord.gg/gtnh"}, "curseForgeAuthorIdsToDiscordLink": {"********": "https://discord.gg/allthemods"}, "customLinks": ["lunapixel.studio/discord"]}}