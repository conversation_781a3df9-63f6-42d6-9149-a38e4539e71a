/*
 * The settings file is used to specify which projects to include in your build.
 *
 * Detailed information about configuring a multi-project build in <PERSON>radle can be found
 * in the user manual at https://docs.gradle.org/7.1.1/userguide/multi_project_builds.html
 */
plugins {
    id("org.gradle.toolchains.foojay-resolver") version "0.9.0"
}

rootProject.name = name

toolchainManagement {
    jvm {
        javaRepositories {
            repository("foojay") {
                resolverClass = org.gradle.toolchains.foojay.FoojayToolchainResolver
            }
        }
    }
}

include('app')
include('legacy-launch')