<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.atlauncher.thread">
    <Appenders>
        <Console name="LogToConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level - %msg%n"/>
        </Console>
        <RollingFile name="LogToRollingFile" fileName="logs/atlauncher.log" filePattern="logs/old/%d{yyyy-MM-dd-HH-mm-ss}.log">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level - %msg%n"/>
            <Policies>
                <OnStartupTriggeringPolicy />
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="logs/old/" maxDepth="1">
                    <IfFileName glob="*.log" />
                    <IfLastModified age="14d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>
    <Loggers>
        <Logger name="com.atlauncher" level="debug" additivity="false">
            <AppenderRef ref="LogToRollingFile"/>
        </Logger>
        <Root level="error">
            <AppenderRef ref="LogToConsole"/>
        </Root>
    </Loggers>
</Configuration>
