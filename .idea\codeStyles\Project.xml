<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="WRAP_WHEN_TYPING_REACHES_RIGHT_MARGIN" value="true" />
    <option name="SOFT_MARGINS" value="120" />
    <JavaCodeStyleSettings>
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000" />
      <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
        <value />
      </option>
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="" withSubpackages="true" static="false" module="true" />
          <package name="" withSubpackages="true" static="true" />
          <emptyLine />
          <package name="java" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="org" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="com" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
        </value>
      </option>
      <option name="ALIGN_TYPES_IN_MULTI_CATCH" value="false" />
      <option name="JD_P_AT_EMPTY_LINES" value="false" />
      <option name="JD_KEEP_EMPTY_LINES" value="false" />
      <option name="JD_DO_NOT_WRAP_ONE_LINE_COMMENTS" value="true" />
      <option name="JD_INDENT_ON_CONTINUATION" value="true" />
    </JavaCodeStyleSettings>
    <JetCodeStyleSettings>
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </JetCodeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="RIGHT_MARGIN" value="120" />
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
      <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
      <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="KEEP_BLANK_LINES_BETWEEN_PACKAGE_DECLARATION_AND_HEADER" value="1" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_RESOURCES" value="false" />
      <option name="ALIGN_MULTILINE_FOR" value="false" />
      <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACES" value="true" />
      <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="RESOURCE_LIST_WRAP" value="5" />
      <option name="EXTENDS_LIST_WRAP" value="1" />
      <option name="THROWS_LIST_WRAP" value="1" />
      <option name="EXTENDS_KEYWORD_WRAP" value="1" />
      <option name="THROWS_KEYWORD_WRAP" value="1" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
      <option name="TERNARY_OPERATION_WRAP" value="5" />
      <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_WRAP" value="1" />
      <option name="WRAP_COMMENTS" value="true" />
    </codeStyleSettings>
    <codeStyleSettings language="kotlin">
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </codeStyleSettings>
  </code_scheme>
</component>