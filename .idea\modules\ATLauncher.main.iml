<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id="ATLauncher:main" external.linked.project.path="$MODULE_DIR$/../.." external.root.project.path="$MODULE_DIR$/../.." external.system.id="GRADLE" external.system.module.group="com.atlauncher" external.system.module.type="sourceSet" external.system.module.version="3.4.39.1" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager">
    <output url="file://$MODULE_DIR$/../../build/classes/java/main" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../build/generated/source/apollo/main/service">
      <sourceFolder url="file://$MODULE_DIR$/../../build/generated/source/apollo/main/service" isTestSource="false" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/generated/sources/annotationProcessor/java/main">
      <sourceFolder url="file://$MODULE_DIR$/../../build/generated/sources/annotationProcessor/java/main" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../src/main">
      <sourceFolder url="file://$MODULE_DIR$/../../src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/../../src/main/resources" type="java-resource" />
    </content>
    <orderEntry type="jdk" jdkName="temurin-17" jdkType="JavaSDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Gradle: com.github.oshi:oshi-core:6.6.6" level="project" />
    <orderEntry type="library" name="Gradle: net.java.dev.jna:jna-platform:5.16.0" level="project" />
    <orderEntry type="library" name="Gradle: net.java.dev.jna:jna:5.16.0" level="project" />
    <orderEntry type="library" name="Gradle: com.google.code.gson:gson:2.11.0" level="project" />
    <orderEntry type="library" name="Gradle: com.google.guava:guava:33.4.0-jre" level="project" />
    <orderEntry type="library" name="Gradle: org.tukaani:xz:1.10" level="project" />
    <orderEntry type="library" name="Gradle: net.iharder:base64:2.3.9" level="project" />
    <orderEntry type="library" name="Gradle: net.sf.jopt-simple:jopt-simple:5.0.4" level="project" />
    <orderEntry type="library" name="Gradle: org.zeroturnaround:zt-zip:1.17" level="project" />
    <orderEntry type="library" name="Gradle: io.sentry:sentry:8.0.0" level="project" />
    <orderEntry type="library" name="Gradle: com.github.RyanTheAllmighty.gettext:gettext-lib:88ae68d897" level="project" />
    <orderEntry type="library" name="Gradle: com.sangupta:murmur:1.0.0" level="project" />
    <orderEntry type="library" name="Gradle: net.freeutils:jlhttp:3.2" level="project" />
    <orderEntry type="library" name="Gradle: joda-time:joda-time:2.13.0" level="project" />
    <orderEntry type="library" name="Gradle: org.commonmark:commonmark:0.21.0" level="project" />
    <orderEntry type="library" name="Gradle: com.github.hypfvieh:dbus-java:3.3.2" level="project" />
    <orderEntry type="library" name="Gradle: com.github.MCRcortex:nekodetector:Version-1.1-pre" level="project" />
    <orderEntry type="library" name="Gradle: com.twelvemonkeys.imageio:imageio-webp:3.12.0" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.commons:commons-compress:1.27.1" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-rx3-support:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-runtime:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-http-cache:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: com.squareup.okhttp3:okhttp:4.12.0" level="project" />
    <orderEntry type="library" name="Gradle: com.squareup.okhttp3:okhttp-tls:4.12.0" level="project" />
    <orderEntry type="library" name="Gradle: com.formdev:flatlaf:3.5.4" level="project" />
    <orderEntry type="library" name="Gradle: com.formdev:flatlaf-extras:3.5.4" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.logging.log4j:log4j-core:2.24.3" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.logging.log4j:log4j-api:2.24.3" level="project" />
    <orderEntry type="library" name="Gradle: com.gitlab.doomsdayrs:rxswing:a5749ad421" level="project" />
    <orderEntry type="library" name="Gradle: io.reactivex.rxjava3:rxjava:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.slf4j:slf4j-api:2.0.16" level="project" />
    <orderEntry type="library" name="Gradle: com.google.errorprone:error_prone_annotations:2.36.0" level="project" />
    <orderEntry type="library" name="Gradle: com.google.guava:failureaccess:1.0.2" level="project" />
    <orderEntry type="library" name="Gradle: com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava" level="project" />
    <orderEntry type="library" name="Gradle: com.google.code.findbugs:jsr305:3.0.2" level="project" />
    <orderEntry type="library" name="Gradle: org.checkerframework:checker-qual:3.43.0" level="project" />
    <orderEntry type="library" name="Gradle: com.google.j2objc:j2objc-annotations:3.0.0" level="project" />
    <orderEntry type="library" name="Gradle: com.tunnelvisionlabs:antlr4-runtime:4.7.3" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-unixsocket:0.38.17" level="project" />
    <orderEntry type="library" name="Gradle: com.twelvemonkeys.imageio:imageio-metadata:3.12.0" level="project" />
    <orderEntry type="library" name="Gradle: com.twelvemonkeys.imageio:imageio-core:3.12.0" level="project" />
    <orderEntry type="library" name="Gradle: com.twelvemonkeys.common:common-image:3.12.0" level="project" />
    <orderEntry type="library" name="Gradle: com.twelvemonkeys.common:common-io:3.12.0" level="project" />
    <orderEntry type="library" name="Gradle: com.twelvemonkeys.common:common-lang:3.12.0" level="project" />
    <orderEntry type="library" name="Gradle: commons-codec:commons-codec:1.17.1" level="project" />
    <orderEntry type="library" name="Gradle: commons-io:commons-io:2.16.1" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.commons:commons-lang3:3.16.0" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-http-cache-api:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: org.reactivestreams:reactive-streams:1.0.4" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-enxio:0.32.13" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-posix:3.1.15" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-ffi:2.2.11" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-constants:0.10.3" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib:1.9.10" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-normalized-cache-jvm:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-api-jvm:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: com.squareup.okio:okio-jvm:3.6.0" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jffi:1.3.9" level="project" />
    <orderEntry type="library" name="Gradle: org.ow2.asm:asm-commons:9.2" level="project" />
    <orderEntry type="library" name="Gradle: org.ow2.asm:asm-util:9.2" level="project" />
    <orderEntry type="library" name="Gradle: org.ow2.asm:asm-analysis:9.2" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Gradle: org.ow2.asm:asm-tree:9.2" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Gradle: org.ow2.asm:asm:9.2" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-a64asm:1.0.0" level="project" />
    <orderEntry type="library" name="Gradle: com.github.jnr:jnr-x86asm:1.0.2" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains:annotations:13.0" level="project" />
    <orderEntry type="library" name="Gradle: com.apollographql.apollo:apollo-normalized-cache-api-jvm:2.5.14" level="project" />
    <orderEntry type="library" name="Gradle: com.benasher44:uuid-jvm:0.2.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.ow2.asm:asm-tree:9.5" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.ow2.asm:asm:9.5" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.github.weisj:jsvg:1.4.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.github.jnr:jffi:native:1.3.9" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.nytimes.android:cache:2.0.2" level="project" />
  </component>
</module>