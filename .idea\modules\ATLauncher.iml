<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id="ATLauncher" external.linked.project.path="$MODULE_DIR$/../.." external.root.project.path="$MODULE_DIR$/../.." external.system.id="GRADLE" external.system.module.group="com.atlauncher" external.system.module.version="3.4.39.1" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$/../..">
      <excludeFolder url="file://$MODULE_DIR$/../../.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/../../build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>