<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" packages="com.mojang.util">
    <Appenders>
        <Console name="FmlSysOut" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %replace{%msg}{(?i)\u00A7[0-9A-FK-OR]}{}%n" />
        </Console>
        <Console name="SysOut" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level]: %replace{%msg}{(?i)\u00A7[0-9A-FK-OR]}{}%n" />
        </Console>
        <Queue name="ServerGuiConsole" ignoreExceptions="true">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %replace{%msg}{(?i)\u00A7[0-9A-FK-OR]}{}%n" />
        </Queue>
        <RollingRandomAccessFile name="File" fileName="logs/latest.log" filePattern="logs/%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level]: %replace{%msg}{(?i)\u00A7[0-9A-FK-OR]}{}%n" />
            <Policies>
                <TimeBasedTriggeringPolicy />
                <OnStartupTriggeringPolicy />
            </Policies>
        </RollingRandomAccessFile>
        <Routing name="FmlFile">
            <Routes pattern="$${ctx:side}">
                <Route>
                    <RollingRandomAccessFile name="FmlFile" fileName="logs/fml-${ctx:side}-latest.log" filePattern="logs/fml-${ctx:side}-%i.log">
                        <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger/%X{mod}]: %replace{%msg}{(?i)\u00A7[0-9A-FK-OR]}{}%n" />
                        <DefaultRolloverStrategy max="3" fileIndex="max" />
                        <Policies>
                            <OnStartupTriggeringPolicy />
                        </Policies>
                    </RollingRandomAccessFile>
                </Route>
                <Route key="$${ctx:side}">
                    <RandomAccessFile name="FmlFile" fileName="logs/fml-junk-earlystartup.log" >
                        <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %replace{%msg}{(?i)\u00A7[0-9A-FK-OR]}{}%n" />
                    </RandomAccessFile>
                </Route>
            </Routes>
        </Routing>
    </Appenders>
    <Loggers>
        <Logger level="info" name="com.mojang" additivity="false">
            <AppenderRef ref="SysOut" level="INFO" />
            <AppenderRef ref="File" />
            <AppenderRef ref="ServerGuiConsole" level="INFO" />
        </Logger>
        <Logger level="info" name="net.minecraft" additivity="false">
            <filters>
                <MarkerFilter marker="NETWORK_PACKETS" onMatch="DENY" onMismatch="NEUTRAL" />
                <RegexFilter regex=".*\$\{[^}]*\}.*" onMatch="DENY" onMismatch="NEUTRAL"/>
            </filters>
            <AppenderRef ref="SysOut" level="INFO" />
            <AppenderRef ref="File" />
            <AppenderRef ref="ServerGuiConsole" level="INFO" />
        </Logger>
        <Root level="all">
	    <filters>
                <MarkerFilter marker="NETWORK_PACKETS" onMatch="DENY" onMismatch="NEUTRAL"/>
                <RegexFilter regex=".*\$\{[^}]*\}.*" onMatch="DENY" onMismatch="NEUTRAL"/>
            </filters>
            <AppenderRef ref="FmlSysOut" level="INFO" />
            <AppenderRef ref="ServerGuiConsole" level="INFO" />
            <AppenderRef ref="FmlFile"/>
        </Root>
    </Loggers>
</Configuration>