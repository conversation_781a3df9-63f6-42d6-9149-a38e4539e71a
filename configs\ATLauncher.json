{"usingCustomJavaPath": false, "firstTimeRun": false, "addedPacks": [], "ignoreOneDriveWarning": false, "ignoreProgramFilesWarning": false, "ignoreJavaOptionsWarning": false, "seenCurseForgeProjectDistributionDialog": false, "seenBundledJrePromptVersion": 0, "seenOutdatedJavaPromptVersion": 0, "rememberWindowSizePosition": false, "consoleSize": {"width": 650, "height": 400}, "consolePosition": {"x": 0, "y": 0}, "launcherSize": {"width": 1200, "height": 700}, "language": "English", "theme": "com.atlauncher.themes.Dark", "dateFormat": "dd/MM/yyyy", "instanceTitleFormat": "%1$s (%2$s %3$s)", "defaultInstanceSorting": "BY_NAME", "selectedTabOnStartup": 0, "keepLauncherOpen": true, "enableConsole": true, "enableTrayMenu": true, "enableFeralGamemode": false, "disableAddModRestrictions": false, "disableCustomFonts": false, "useNativeFilePicker": false, "useRecycleBin": true, "enableArmSupport": true, "defaultModPlatform": "Curse<PERSON><PERSON>ge", "defaultExportFormat": "CURSEFORGE", "addModRestriction": "STRICT", "enableAddedModsByDefault": true, "showFabricModsWhenSinytraInstalled": true, "allowCurseForgeAlphaBetaFiles": false, "dontCheckModsOnCurseForge": false, "dontCheckModsOnModrinth": false, "scanModsOnLaunch": true, "maximumMemory": 4096, "metaspace": 256, "windowWidth": 854, "windowHeight": 480, "javaPath": "C:\\Program Files\\Java\\jdk1.8.0_202", "javaParameters": "-XX:+UnlockExperimentalVMOptions -XX:+UseG1GC -XX:G1NewSizePercent=20 -XX:G1ReservePercent=20 -XX:MaxGCPauseMillis=50 -XX:G1HeapRegionSize=32M", "maximiseMinecraft": false, "ignoreJavaOnInstanceLaunch": false, "useJavaProvidedByMinecraft": true, "disableLegacyLaunching": false, "useSystemGlfw": false, "useSystemOpenAl": false, "environmentVariables": {}, "concurrentConnections": 8, "connectionTimeout": 60, "enableProxy": false, "proxyHost": "", "proxyPort": 8080, "proxyType": "HTTP", "enableLogs": true, "enableAnalytics": true, "analyticsClientId": "87dbac36-62d4-4216-80d8-a6d3dca5ba46", "enableAutomaticBackupAfterLaunch": false, "backupMode": "NORMAL", "enableCommands": false, "hasFixedSelectedTabOnStartup_3_4_13_5": true}