/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.utils;

import org.zeroturnaround.zip.NameMapper;

import com.atlauncher.data.BackupMode;

public class ZipNameMapper {
    public static final NameMapper NORMAL_BACKUP = name -> {
        if (name.equalsIgnoreCase("options.txt") || name.startsWith("saves") || name.startsWith("config")) {
            return name;
        }

        return null;
    };

    public static final NameMapper NORMAL_PLUS_MODS_BACKUP = name -> {
        if (name.equalsIgnoreCase("options.txt") || name.startsWith("saves") || name.startsWith("config")
                || name.startsWith("mods") || name.startsWith("jarmods") || name.startsWith("coremods")) {
            return name;
        }

        return null;
    };

    public static final NameMapper FULL_BACKUP = name -> name;

    public static NameMapper getMapperForBackupMode(BackupMode backupMode) {
        if (backupMode == BackupMode.NORMAL) {
            return NORMAL_BACKUP;
        }

        if (backupMode == BackupMode.NORMAL_PLUS_MODS) {
            return NORMAL_PLUS_MODS_BACKUP;
        }

        return FULL_BACKUP;
    }
}
