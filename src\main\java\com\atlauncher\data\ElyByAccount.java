/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.data;

import java.util.Date;

import com.atlauncher.data.elyby.ElyByOAuthTokenResponse;
import com.atlauncher.data.elyby.ElyByProfile;
import com.atlauncher.gui.dialogs.LoginWithElyByDialog;
import com.atlauncher.gui.dialogs.ProgressDialog;
import com.atlauncher.managers.AccountManager;
import com.atlauncher.managers.DialogManager;
import com.atlauncher.managers.LogManager;
import com.atlauncher.network.DownloadException;
import com.atlauncher.utils.ElyByAuthAPI;
import org.mini2Dx.gettext.GetText;

public class ElyByAccount extends AbstractAccount {
    /**
     * Auto generated serial.
     */
    private static final long serialVersionUID = 5483749902584257560L;

    /**
     * The access token.
     */
    public String accessToken;

    /**
     * The refresh token.
     */
    public String refreshToken;

    /**
     * The date that the accessToken expires at.
     */
    public Date accessTokenExpiresAt;

    /**
     * If the user must login again. This is usually the result of a failed accessToken refresh.
     */
    public boolean mustLogin;

    public ElyByAccount(ElyByOAuthTokenResponse tokenResponse, ElyByProfile profile) {
        update(tokenResponse, profile);
    }

    public void update(ElyByOAuthTokenResponse tokenResponse, ElyByProfile profile) {
        this.accessToken = tokenResponse.accessToken;
        this.refreshToken = tokenResponse.refreshToken;
        this.minecraftUsername = profile.username;
        this.uuid = profile.uuid;
        this.username = profile.username;
        this.mustLogin = false;

        this.accessTokenExpiresAt = new Date();
        this.accessTokenExpiresAt.setTime(this.accessTokenExpiresAt.getTime() + (tokenResponse.expiresIn * 1000L));
    }

    @Override
    public String getAccessToken() {
        return accessToken;
    }

    @Override
    public String getSessionToken() {
        return accessToken;
    }

    @Override
    public String getUserType() {
        return "elyby";
    }

    @Override
    public String getCurrentUsername() {
        ElyByProfile profile = null;

        try {
            profile = ElyByAuthAPI.getProfile(accessToken);
        } catch (Exception e) {
            LogManager.error("Error getting Ely.by profile");
            return null;
        }

        if (profile == null) {
            LogManager.error("Error getting Ely.by profile");
            return null;
        }

        return profile.username;
    }

    @Override
    public void updateSkinPreCheck() {
        this.refreshAccessToken();
    }

    @Override
    public void changeSkinPreCheck() {
        this.refreshAccessToken();
    }

    @Override
    public String getSkinUrl() {
        // Ely.by uses a different skin system, for now return null to use default skin
        // TODO: Implement Ely.by skin URL retrieval
        return null;
    }

    public void updateUsernameFromElyBy() {
        ElyByProfile profile = null;

        try {
            profile = ElyByAuthAPI.getProfile(accessToken);
        } catch (Exception e) {
            LogManager.logStackTrace("Error getting Ely.by profile", e);
            return;
        }

        if (profile == null) {
            LogManager.error("Error getting Ely.by profile");
            return;
        }

        this.minecraftUsername = profile.username;
        this.username = profile.username;
    }

    public boolean isReal() {
        return true;
    }

    public void login() {
        final ProgressDialog dialog = new ProgressDialog(GetText.tr("Logging Into Ely.by"), 0,
                GetText.tr("Logging Into Ely.by"), "Aborting login for " + this.minecraftUsername);
        dialog.addThread(new Thread(() -> {
            dialog.setReturnValue(refreshAccessToken());
            dialog.close();
        }));
        dialog.start();

        if (!(Boolean) dialog.getReturnValue()) {
            DialogManager.okDialog().setTitle(GetText.tr("Failed To Login"))
                    .setContent(GetText.tr("Failed to login to Ely.by. Please login again."))
                    .setType(DialogManager.ERROR).show();

            LoginWithElyByDialog loginDialog = new LoginWithElyByDialog(this);
            loginDialog.setVisible(true);
        }
    }

    public boolean refreshAccessToken() {
        return refreshAccessToken(false);
    }

    public boolean refreshAccessToken(boolean force) {
        try {
            if (force || new Date().after(this.accessTokenExpiresAt)) {
                ElyByOAuthTokenResponse tokenResponse = ElyByAuthAPI.refreshAccessToken(this.refreshToken);

                if (tokenResponse == null) {
                    mustLogin = true;
                    AccountManager.saveAccounts();
                    LogManager.error("Failed to refresh Ely.by access token");
                    return false;
                }

                // Update profile information
                ElyByProfile profile = ElyByAuthAPI.getProfile(tokenResponse.accessToken);
                if (profile == null) {
                    mustLogin = true;
                    AccountManager.saveAccounts();
                    LogManager.error("Failed to get Ely.by profile after token refresh");
                    return false;
                }

                this.mustLogin = false;
                this.accessToken = tokenResponse.accessToken;
                // Note: Ely.by doesn't return a new refresh token on refresh, so we keep the old one

                this.accessTokenExpiresAt = new Date();
                this.accessTokenExpiresAt.setTime(this.accessTokenExpiresAt.getTime() + (tokenResponse.expiresIn * 1000L));

                AccountManager.saveAccounts();
            }
        } catch (Exception e) {
            mustLogin = true;
            AccountManager.saveAccounts();

            LogManager.logStackTrace("Exception refreshing Ely.by accessToken", e);
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        return this.minecraftUsername + " (Ely.by)";
    }
}
