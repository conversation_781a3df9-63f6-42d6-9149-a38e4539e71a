name: 'Label cleanup'
description: 'Add comment, remove label and then close issues with a specific label'
branding:
  icon: user-check
  color: gray-dark
inputs:
  label:
    description: 'The label to action'
    required: true
  comment:
    description: 'The comment to leave on the comments'
    required: true
  token:
    description: 'The GitHub token to use'
    required: false
    default: ${{ github.token }}
runs:
  using: 'docker'
  image: 'Dockerfile'
