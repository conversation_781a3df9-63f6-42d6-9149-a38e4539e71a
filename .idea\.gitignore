# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
**/workspace.xml
**/tasks.xml
**/usage.statistics.xml
**/dictionaries
**/shelf

# AWS User-specific
**/aws.xml

# Generated files
**/contentModel.xml

# Sensitive or high-churn files
**/dataSources/
**/dataSources.ids
**/dataSources.local.xml
**/sqlDataSources.xml
**/dynamic.xml
**/uiDesigner.xml
**/dbnavigator.xml

# Gradle
**/gradle.xml
**/libraries

# Mongo Explorer plugin
**/mongoSettings.xml

# Cursive Clojure plugin
replstate.xml

# SonarLint plugin
sonarlint/
sonarlint.xml # see https://community.sonarsource.com/t/is-the-file-idea-idea-idea-sonarlint-xml-intended-to-be-under-source-control/121119

# Editor-based Rest Client
httpRequests

# Android studio 3.1+ serialized cache file
caches/build_file_checksums.ser
