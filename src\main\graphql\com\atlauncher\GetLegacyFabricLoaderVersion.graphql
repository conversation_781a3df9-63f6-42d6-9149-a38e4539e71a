query GetLegacyFabricLoaderVersion(
    $legacyFabricVersion: String!
    $minecraftVersion: String!
    $includeClientJson: Boolean = false
    $includeServerJson: Boolean = false
) {
    legacyFabricLoaderVersion(version: $legacyFabricVersion, minecraftVersion: $minecraftVersion) {
        version
        stable
        clientJson @include(if: $includeClientJson)
        serverJson @include(if: $includeServerJson)
    }
}
