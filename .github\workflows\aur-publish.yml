name: aur-publish

on:
  repository_dispatch:
    types: [aur-publish]

jobs:
  aur-publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Publish atlauncher AUR package
        uses: ./.github/actions/publish-to-aur
        with:
          version: ${{ github.event.client_payload.version }}
          release: ${{ github.event.client_payload.release || '1' }}
          workingDir: ./packaging/aur/atlauncher
          packageName: atlauncher
          aurUsername: ${{ secrets.AUR_USERNAME }}
          aurEmail: ${{ secrets.AUR_EMAIL }}
          aurSshPrivateKey: ${{ secrets.AUR_SSH_PRIVATE_KEY }}
          aurCommitMessage: Update to version ${{ github.event.client_payload.version }}
          packagesToInstall: jdk17-openjdk openal

      - name: Publish atlauncher-bin AUR package
        uses: ./.github/actions/publish-to-aur
        with:
          version: ${{ github.event.client_payload.version }}
          release: ${{ github.event.client_payload.release || '1' }}
          workingDir: ./packaging/aur/atlauncher-bin
          packageName: atlauncher-bin
          aurUsername: ${{ secrets.AUR_USERNAME }}
          aurEmail: ${{ secrets.AUR_EMAIL }}
          aurSshPrivateKey: ${{ secrets.AUR_SSH_PRIVATE_KEY }}
          aurCommitMessage: Update to version ${{ github.event.client_payload.version }}
          packagesToInstall: jdk17-openjdk openal unzip

      - name: Commit changed files
        uses: EndBug/add-and-commit@v9
        with:
          add: "packaging/aur/"
          message: "chore: update aur packages [skip ci]"
