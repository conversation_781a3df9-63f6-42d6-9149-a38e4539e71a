{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "./gradlew build",
            "dependsOn": [
                "clean"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "label": "createTestLauncherDir",
            "type": "shell",
            "command": "./gradlew createTestLauncherDir",
            "problemMatcher": [],
            "presentation": {
                "reveal": "silent",
                "revealProblems": "onProblem",
                "close": true
            }
        },
        {
            "label": "generateTemplatePot",
            "type": "shell",
            "command": "./gradlew generatePots",
            "problemMatcher": []
        },
        {
            "label": "checkForDependencyUpdates",
            "type": "shell",
            "command": "./gradlew dependencyUpdates",
            "problemMatcher": []
        },
        {
            "label": "clean",
            "type": "shell",
            "command": "./gradlew clean",
            "group": "build"
        },
        {
            "label": "test",
            "type": "shell",
            "command": "./gradlew test",
            "group": {
                "kind": "test",
                "isDefault": true
            }
        }
    ]
}
