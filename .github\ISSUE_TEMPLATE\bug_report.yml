name: Bug Report
description: File a bug report
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

        # STOP AND READ THE BELOW FIRST

        If you haven't been directed here by a member of the team, please visit our [Discord](https://atl.pw/discord) to verify that your bug is correct and should be reported here. Bug reports submitted here without first being verified by ATLauncher staff may get closed.

        Failing to follow the above will result in issues being closed and directed to our [Discord](https://atl.pw/discord) first.

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: |
        1. Open ATLauncher
        2. I then did .....
        3. And then .... error happens
        4. I expected .....
    validations:
      required: true

  - type: textarea
    id: operating-system
    attributes:
      label: Operating System
      description: Please give us details about your operating system including what version of the operating system you're using. If you're using Linux, please also include the distribution (e.g. Ubuntu, Arch, etc) and version you're using, the desktop environment you're using (e.g. KDE, Gnome, etc) as well as the Window Manager (e.g. i3, Openbox, etc).
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output.
      render: shell

  - type: textarea
    id: additional-information
    attributes:
      label: Additional Information
      description: Any additional information including images or video can be left here.

  - type: checkboxes
    id: launcher-bug-confirm
    attributes:
      label: Is this actually a launcher bug?
      description: GitHub issues is not the place for issues with your game or for issues with the launcher (such as failing to install or download mods/modpacks). For those please visit [our Discord](https://atl.pw/discord).
      options:
        - label: I am sure this is a bug with the launcher and not an issue with running Minecraft
          required: true

  - type: checkboxes
    id: already-exists-check
    attributes:
      label: Have you checked this issue doesn't already exist?
      description: Please make sure you've checked the issues already in this repository to see if this bug (or something similar) already exists, if so, comment on the existing issue instead.
      options:
        - label: I am sure this issue doesn't exist already
    validations:
      required: true

  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/ATLauncher/ATLauncher/blob/master/CODE_OF_CONDUCT.md)
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
