# suppress inspection "UnusedProperty" for whole file
#
# ATLauncher - https://github.com/ATLauncher/ATLauncher
# Copyright (C) 2013-2020 ATLauncher
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.
#
# This inherits from
# ./Dark.properties
# ./ATLauncherLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatDarkLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLaf.properties

#---- variables ----
black=#000000
consoleBackground=#191919
editorBackground=#212121
panelBackground=#333333
textColor=#F9F6EF
border=#3c3c3c
redMac=#fb3b45
redWin=#ef6950
redMonokaiPro=#ff6188
blueWinPalette=#409AE1
blue=#78dce8
orangeMonokaiPro=#fc9867
greenMonokaiPro=#a9dc76
yellowWinPalette=#ffc83d
yellowMac=#faaa1f
purpleWinPalette=#b4a0ff
purpleMonokaiPro=#ab9df2
greenWinPalette=#40c5af
greyWinPalette=#b2b2b2
panelText=#a6a6a6
desaturatedBlue=#1e282d
desaturatedOrange=#8049117f
green=#5B8021
greyDot15=#d8d8d8
greyDot20=#cccccc
greyDot25=#bfbfbf
greyDot33=#aaaaaa
greyDot50=#7d7d7d
greyDot60=#666666
greyDot65=#5a5a5a
greyDot70=#4d4d4d
greyDot75=#434343
greyDot80=#323232
greyDot85=#252525
greyDot90=#1f2021
lightBlue=#70D7FF
navyDot85=#191d21
navyDot90=#1f2021
red=#800040
transparentGreen=#5B80217f
transparentRed=#8000407f
transparentViolet=#9478F67f
transparentYellow=#8066357f
yellow=#806635

#---- override base variables ----
@background=$greyDot80
@foreground=$greyDot20
separatorColor=#515151
buttonBackground=$greyDot80

#---- all the things ----
*.arc=3
*.shadow=$greyDot75
*.background=$greyDot80
*.borderColor=$greyDot70
*.caretForeground=$yellowMac
*.color=$greyDot50
*.foreground=$greyDot20
*.hoverBackground=$greyDot70
*.selectedBackground=$greyDot85
*.selectedForeground=$greyDot15
*.selectedInactiveBackground=$greyDot70
*.selectionBackground=$navyDot85
*.selectionForeground=$yellowMac
*.separatorColor=$greyDot75
ActionButton.hoverBorderColor=$greyDot50
ActionButton.pressedBackground=$greyDot65
ActionButton.pressedBorderColor=$greyDot50
Borders.ContrastBorderColor=$greyDot65
Borders.color=$border
Button.default.endBackground=$greyDot80
Button.default.endBorderColor=$greyDot65
Button.default.focusColor=$greyDot80
Button.default.focusedBorderColor=$greyDot15
Button.default.foreground=$greyDot15
Button.default.startBackground=$greyDot80
Button.default.startBorderColor=$greyDot65
Button.endBackground=$greyDot80
Button.endBorderColor=$greyDot65
Button.focusedBorderColor=$yellowMac
Button.startBackground=$greyDot80
Button.startBorderColor=$greyDot65
CheckBox.disabledText=$greyDot33
CheckBox.select=$greyDot50
CheckBoxMenuItem.disabledBackground=$greyDot80
ComboBox.ArrowButton.disabledIconColor=$greyDot50
ComboBox.ArrowButton.iconColor=$yellowMac
ComboBox.ArrowButton.nonEditableBackground=$greyDot70
ComboBox.modifiedItemForeground=$yellowMac
ComboBox.disabledForeground=$greyDot80
ComboBox.nonEditableBackground=$yellowMac
ComboPopup.border=#4d4d4d
CompletionPopup.nonFocusedMask=#34343434
CompletionPopup.selectionBackground=$navyDot85
CompletionPopup.foreground=$greyDot20
CompletionPopup.matchForeground=$yellowMac
Component.arc=3
Component.disabledBorderColor=$greyDot70
Component.infoForeground=$greyDot50
Component.errorFocusColor=#F65F87
Component.focusColor=$greyDot50
Component.focusWidth=0
Component.focusedBorderColor=$greyDot50
Component.hoverIconColor=$yellowMac
Component.inactiveErrorFocusColor=$transparentRed
Component.inactiveWarningFocusColor=$transparentYellow
Component.warningFocusColor=$yellow
Debugger.Variables.changedValueForeground=$yellowMac
Debugger.Variables.evaluatingExpressionForeground=$lightBlue
DefaultTabs.underlineColor=$yellowMac
DefaultTabs.underlinedTabBackground=$greyDot75
DefaultTabs.underlinedTabForeground=$lightBlue
DragAndDrop.areaBackground=$greyDot75
DragAndDrop.areaForeground=$greyDot25
Editor.background=$greyDot90
EditorPane.inactiveForeground=$greyDot50
FileColor.Blue=#23282d
FileColor.Green=#232d28
FileColor.Orange=#2d2823
FileColor.Rose=#2d2323
FileColor.Violet=#2D232D
FileColor.Yellow=#2d2d23
FormattedTextField.inactiveBackground=$greyDot80
FormattedTextField.background=$greyDot75
GutterTooltip.infoForeground=$greyDot50
Label.foreground=$greyDot25
Label.infoForeground=$greyDot50
Link.activeForeground=$lightBlue
Link.hoverForeground=$yellowMac
Link.pressedForeground=$lightBlue
Link.visitedForeground=$greyDot25
MemoryIndicator.allocatedBackground=$green
MemoryIndicator.usedBackground=$red
Menu.separatorColor=$greyDot75
Menu.foreground=$textColor
Menu.borderColor=$greyDot70
Menu.acceleratorForeground=$greyDot25
Notification.MoreButton.innerBorderColor=$greyDot65
Notification.ToolWindow.errorBackground=$greyDot85
Notification.ToolWindow.errorBorderColor=#ed005c
Notification.ToolWindow.errorForeground=#F65F87
Notification.ToolWindow.informativeBackground=$greyDot85
Notification.ToolWindow.informativeBorderColor=#92D923
Notification.ToolWindow.informativeForeground=#92D923
Notification.ToolWindow.warningBackground=$greyDot85
Notification.ToolWindow.warningBorderColor=$yellowMac
Notification.ToolWindow.warningForeground=$yellowMac
Notification.background=$greyDot85
Notification.errorBackground=$greyDot85
Notification.errorBorderColor=#ed005c
Notification.errorForeground=#F65F87
OptionPane.background=$greyDot80
OptionPane.foreground=$greyDot20
Panel.background=$greyDot80
Panel.foreground=$greyDot20
ParameterInfo.background=$greyDot85
ParameterInfo.foreground=$greyDot25
ParameterInfo.currentOverloadBackground=$greyDot65
ParameterInfo.currentParameterForeground=$yellowMac
ParameterInfo.infoForeground=$greyDot33
ParameterInfo.lineSeparatorColor=$greyDot75
PasswordField.background=$greyDot75
Plugins.Button.installBackground=$greyDot80
Plugins.Button.installBorderColor=$greyDot65
Plugins.Button.installFillBackground=$greyDot80
Plugins.Button.installFillForeground=$greyDot25
Plugins.Button.installForeground=$yellowMac
Plugins.SearchField.background=$greyDot75
Plugins.SectionHeader.background=$greyDot75
Plugins.Tab.hoverBackground=$navyDot85
Plugins.Tab.selectedBackground=$greyDot85
Plugins.background=$greyDot80
Plugins.disabledForeground=$greyDot50
Plugins.lightSelectionBackground=$navyDot85
Plugins.tagBackground=$greyDot85
Popup.Advertiser.background=$greyDot85
Popup.Advertiser.foreground=$greyDot50
Popup.Header.activeBackground=$greyDot75
Popup.Header.inactiveBackground=$greyDot85
PopupMenu.background=$greyDot80
ProgressBar.failedColor=#ed005c
ProgressBar.failedEndColor=$greyDot75
ProgressBar.indeterminateStartColor=$yellowMac
ProgressBar.indeterminateEndColor=#FD971F
ProgressBar.passedColor=#92D923
ProgressBar.passedEndColor=$greyDot75
ProgressBar.progressColor=$yellowMac
ProgressBar.trackColor=$greyDot75
RadioButton.background=$greyDot80
RadioButtonMenuItem.disabledBackground=$greyDot80
ScrollPane.background=$greyDot80
SearchEverywhere.Advertiser.foreground=$greyDot50
SearchEverywhere.List.separatorForeground=$greyDot50
SearchEverywhere.SearchField.infoForeground=$greyDot33
SearchEverywhere.SearchField.background=$greyDot75
SearchEverywhere.Header.background=$greyDot80
SearchEverywhere.Tab.selectedBackground=$greyDot85
SearchEverywhere.Tab.selectedForeground=$lightBlue
SearchMatch.endBackground=$yellowMac
SearchMatch.startBackground=$yellowMac
SidePanel.background=$greyDot85
SpeedSearch.errorForeground=#F65F87
SpeedSearch.foreground=$yellowMac
SplitPane.highlight=$yellowMac
PopupMenu.translucentBackground=$greyDot50
TabbedPane.disabledUnderlineColor=$greyDot65
TabbedPane.focusColor=$greyDot65
TabbedPane.underlineColor=$yellowMac
TabbedPane.inactiveUnderlineColor=$yellowMac
Table.dropLineColor=$greyDot75
Table.dropLineShortColor=$greyDot70
Table.focusCellBackground=$greyDot85
Table.focusCellForeground=$yellowMac
Table.sortIconColor=$yellowMac
Table.stripeColor=$greyDot75
TableHeader.background=$greyDot85
TableHeader.bottomSeparatorColor=$greyDot65
TextArea.background=$greyDot75
TextArea.caretForeground=$yellowMac
TextArea.inactiveBackground=$greyDot80
TextField.background=$greyDot75
TextField.foreground=$greyDot25
TextField.caretForeground=$yellowMac
TextField.highlight=$greyDot15
TextField.inactiveForeground=$greyDot33
TextPane.inactiveBackground=$greyDot80
TitlePane.background=$greyDot85
ToggleButton.buttonColor=$greyDot65
ToggleButton.offBackground=$greyDot75
ToggleButton.offForeground=$greyDot25
ToggleButton.onBackground=$yellowMac
ToggleButton.onForeground=$greyDot80
ToolBar.borderHandleColor=$greyDot65
ToolTip.Actions.background=$greyDot80
ToolTip.Actions.infoForeground=$greyDot50
ToolTip.background=$greyDot75
ToolTip.infoForeground=$greyDot50
ToolWindow.Button.hoverBackground=$greyDot65
ToolWindow.Button.selectedBackground=$greyDot85
ToolWindow.Button.selectedForeground=$lightBlue
ToolWindow.Header.background=$greyDot85
ToolWindow.Header.inactiveBackground=$greyDot80
ToolWindow.HeaderTab.hoverBackground=$greyDot65
ToolWindow.HeaderTab.hoverInactiveBackground=$greyDot85
ToolWindow.HeaderTab.inactiveUnderlineColor=$greyDot75
ToolWindow.HeaderTab.underlineColor=$yellowMac
ToolWindow.HeaderTab.underlinedTabBackground=$greyDot90
ToolWindow.HeaderTab.underlinedTabInactiveBackground=$greyDot75
Tree.background=$greyDot85
Tree.foreground=$greyDot15
Tree.modifiedItemForeground=$yellowMac
ValidationTooltip.errorBackground=$greyDot85
ValidationTooltip.errorBorderColor=#ed005c
ValidationTooltip.warningBackground=$greyDot85
ValidationTooltip.warningBorderColor=$yellowMac
VersionControl.FileHistory.Commit.selectedBranchBackground=$greyDot70
VersionControl.Log.Commit.currentBranchBackground=$greyDot85
VersionControl.Log.Commit.unmatchedForeground=$greyDot25
WelcomeScreen.Projects.selectionBackground=$navyDot85
WelcomeScreen.Projects.selectionInactiveBackground=$navyDot90
WelcomeScreen.separatorColor=$greyDot65
Window.border=#4d4d4d
