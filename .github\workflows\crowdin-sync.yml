name: Crowdin Sync

on:
  push:
    branches: [master]
    paths:
      - "src/main/java/com/atlauncher/**"

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install gettext
        run: sudo apt-get install -y gettext

      - name: Set up JDK 1.8
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 8

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          validate-wrappers: true
          add-job-summary: never

      - name: Generate translations.pot
        run: ./gradlew generatePots

      - name: Deduplicate translations.pot
        run: msguniq -o build/gettext/launcher.pot build/gettext/translations.pot

      - name: Upload source to crowdin
        uses: crowdin/github-action@v2
        with:
          upload_sources: true
          upload_translations: false
          download_translations: false
          push_translations: false
          upload_sources_args: "--dest=launcher.pot"
          source: build/gettext/launcher.pot
          translation: /src/main/resources/assets/lang/%locale%.po
          project_id: ${{ secrets.CROWDIN_PROJECT_ID }}
          token: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
