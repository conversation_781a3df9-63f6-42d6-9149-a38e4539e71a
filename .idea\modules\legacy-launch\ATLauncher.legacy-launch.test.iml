<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":legacy-launch:test" external.linked.project.path="$MODULE_DIR$/../../../legacy-launch" external.root.project.path="$MODULE_DIR$/../../.." external.system.id="GRADLE" external.system.module.group="com.atlauncher" external.system.module.type="sourceSet" external.system.module.version="3.4.39.1" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager">
    <output-test url="file://$MODULE_DIR$/../../../legacy-launch/build/classes/java/test" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../../legacy-launch/src/test" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="ATLauncher.legacy-launch.main" />
  </component>
  <component name="TestModuleProperties" production-module="ATLauncher.legacy-launch.main" />
</module>