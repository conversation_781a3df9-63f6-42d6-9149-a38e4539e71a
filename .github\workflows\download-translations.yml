name: Download Translations

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to download the approved translations to"
        default: "master"
        required: true

jobs:
  download-translations:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch }}

      - name: Download translations from crowdin
        uses: crowdin/github-action@v2
        with:
          upload_sources: false
          upload_translations: false
          download_translations: true
          skip_untranslated_strings: true
          export_only_approved: true
          push_translations: false
          source: build/gettext/launcher.pot
          translation: /src/main/resources/assets/lang/%locale%.po
          download_translations_args: "--all --verbose"
          project_id: ${{ secrets.CROWDIN_PROJECT_ID }}
          token: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}

      - name: Commit changed files
        uses: EndBug/add-and-commit@v9
        with:
          add: "src/main/resources/assets/lang/"
          message: "chore: update translations"
          new_branch: ${{ inputs.branch }}
