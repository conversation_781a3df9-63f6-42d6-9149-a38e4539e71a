# suppress inspection "UnusedProperty" for whole file
#
# ATLauncher - https://github.com/ATLauncher/ATLauncher
# Copyright (C) 2013-2020 ATLauncher
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.
#

# This inherits from
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLaf.properties

# This file is the base of all ATLaucher themes. The variables defined
# here, and defaults defined here will flow into all other themes.

#---- variables ----
primary.100=#f3f9eb
primary.200=#e2f0cd
primary.300=#d0e7af
primary.400=#acd472
primary.500=#89c236
primary.600=#7baf31
primary.700=#527420
primary.800=#3e5718
primary.900=#293a10

secondary.100=#ebebeb
secondary.200=#cecece
secondary.300=#b1b1b1
secondary.400=#767676
secondary.500=#3b3b3b
secondary.600=#333333
secondary.700=#2e2e2e
secondary.800=#1b1b1b
secondary.900=#121212

gray.100=#f7fafc
gray.200=#edf2f7
gray.300=#e2e8f0
gray.400=#cbd5e0
gray.500=#a0aec0
gray.600=#718096
gray.700=#4a5568
gray.800=#2d3748
gray.900=#1a202c

red=#c53030
yellow=#f6e05e

black=#000000
white=#ffffff
transparent=#00000000

#---- override variables from FlatLaf ----
@selectionBackground=$primary.200
@selectionForeground=$black
Separator.foreground=$separatorColor

#---- globals ----
*.selectionInactiveBackground=#3a3f4b
*.selectionBackgroundInactive=#0e293f
*.errorForeground=#ef5350

#---- BottomBar ----
BottomBar.dividerColor=$Separator.foreground

#---- Button ----
Button.background=$buttonBackground
Button.default.shadow=$red
Button.focusedBackground=$buttonBackground
Button.focusedBorderColor=$focusedBorder
Button.hoverBorderColor=$focusedBorder

Button.default.background=$buttonBackground
Button.default.focusedBackground=$buttonBackground
Button.default.focusedBorderColor=$focusedBorder
Button.default.hoverBorderColor=$focusedBorder

#---- Checkbox ----
CheckBox.icon.borderColor=$border
CheckBox.icon.selectedBorderColor=$border
CheckBox.icon.background=$checkboxBackground
CheckBox.icon.checkmarkColor=$checkboxCheck
CheckBox.icon.focusedSelectedBorderColor=$focusedBorder
CheckBox.icon.focusedBorderColor=$focusedBorder

#---- CollapsiblePanel ----
CollapsiblePanel.normal=@foreground
CollapsiblePanel.warning=$yellow
CollapsiblePanel.error=$red

#---- Component ----
Component.focusWidth=0
Component.focusColor=$transparent
Component.innerFocusWidth=0
Component.arrowType=chevron
Component.borderColor=$border
Component.disabledBorderColor=$border
Component.focusedBorderColor=$focusedBorder

#---- Console ----
Console.fontSize={float}12
Console.LogType.debug=#9f7aea
Console.LogType.error=$red
Console.LogType.info=$primary.500
Console.LogType.warn=$yellow
Console.LogType.default=@foreground

#---- EditorPane ----
EditorPane.disabledBackground=@background
EditorPane.inactiveBackground=@background

#---- HoverLineBorder ----
HoverLineBorder.borderColor=$primary.500
HoverLineBorder.insetWidth=4
HoverLineBorder.borderWidth=1

#---- Mods ----
Mods.modSelectionColor=$secondary.600

#---- ModsJCheckBox ----
ModsJCheckBox.hoverBorderColor=#50aa6b

#---- News ----
News.headerColor=$primary.500
News.linkColor=$primary.500

#---- ProgressBar ----
ProgressBar.arc=0
ProgressBar.foreground=$primary.500

#---- ScrollPane ----
ScrollPane.disabledBackground=@background
ScrollPane.inactiveBackground=@background
ScrollPane.arc = 0
ScrollPane.TextComponent.arc = 4

#---- SMButton ----
SMButton.hoverBorderColor=#50aa6b

#---- SocialIcon ----
SocialIcon.backgroundColor=$secondary.500

#---- SplitPane ----
SplitPane.dividerSize={integer}0

#---- TabbedPane ----
TabbedPane.tabSelectionHeight=3
TabbedPane.focusColor=$tabActive
TabbedPane.hoverColor=$tabActive
TabbedPane.underlineColor=$primary.500
TabbedPane.inactiveUnderlineColor=$primary.500
TabbedPane.contentAreaColor=$Separator.foreground
TabbedPane.tabSeparatorColor=$Separator.foreground
TabbedPane.tabSeparatorsFullHeight=true
TabbedPane.showTabSeparators=true

#---- TextArea ----
TextArea.disabledBackground=@background
TextArea.inactiveBackground=@background

#---- TextComponent ----
TextComponent.arc=5

#---- TextPane ----
TextPane.disabledBackground=@background
TextPane.inactiveBackground=@background

#---- Toaster ----
Toaster.bgColor=@background
Toaster.msgColor=@foreground
Toaster.borderColor=$primary.500
Toaster.opacity={float}0.5
Toaster.opaque=false
Toaster.time={integer}5000

#---- ToolPanel ----
ToolPanel.borderColor=$secondary.500

#---- ToolTip ----
ToolTip.background=@background
