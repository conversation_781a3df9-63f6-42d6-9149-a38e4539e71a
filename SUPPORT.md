# Getting support for ATLauncher

Hello there :wave:.

Thanks for taking a look at this file.

If you've come to our GitHub looking for support then you've come to the wrong place :disappointed:.

## Launcher issues

If you're looking for support for:

* Launcher install issues
* Pack install issues

Then please visit our [Discord server](https://atl.pw/discord) and post in one of the support channels, where the
community and contributors to ATLauncher will be able to help you resolve your issue.

If we find that it's an issue with the launcher code itself, then we will raise issues (or ask you to raise them) in the
correct place once triaged.

## Pack/Mod issues

If you're looking for support for:

* Pack feature request
* Pack bug reports
* Mod issues

Then please note that ATLauncher only hosts the modpacks, we do not create, maintain or even have the power to fix or
make the changes you may be looking for/having.

Each modpack is developed by a person (or team) and they're responsible for providing support. Your first step should be
to reach out to the person (or team) who created the modpack.

You can generally find contact information on the launcher or website information pages for the pack. Use the `Get Help`
button on the instance within ATLauncher to find places to get help.

If you fail to find support then you can visit our [Discord server](https://atl.pw/discord) and post in one of the
support channels.
