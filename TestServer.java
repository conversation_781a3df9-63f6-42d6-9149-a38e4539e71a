import java.io.IOException;
import java.net.ServerSocket;

public class TestServer {
    public static void main(String[] args) {
        int port = 28563;
        
        System.out.println("Testing if port " + port + " is available...");
        
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            System.out.println("✅ Port " + port + " is available and can be bound");
            System.out.println("Server started successfully on port " + port);
            
            // Keep server running for a few seconds
            Thread.sleep(5000);
            
        } catch (IOException e) {
            System.err.println("❌ Failed to bind to port " + port + ": " + e.getMessage());
            System.err.println("This could mean:");
            System.err.println("1. Port is already in use by another application");
            System.err.println("2. Permission denied (try running as administrator)");
            System.err.println("3. Firewall is blocking the port");
        } catch (InterruptedException e) {
            System.err.println("Thread interrupted");
        }
        
        System.out.println("Test completed.");
    }
}
