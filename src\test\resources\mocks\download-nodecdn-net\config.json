{"useLwjglReplacement": false, "analytics": {"enabled": false, "enabledVersion": "0.0.0.0", "percentage": 0, "autoSendInSeconds": 300}, "useGraphql": {"vanillaLoaderVersions": false, "loaderVersions": false, "loaderVersionsNonForge": false, "packActions": false, "news": false, "unifiedModPacks": false, "minecraftVersions": false, "javaRuntimes": false, "launcherLaunch": false}, "minecraft": {"experiment": {"enabled": true, "disabledVersions": []}, "snapshot": {"enabled": true, "disabledVersions": []}, "release": {"enabled": true, "disabledVersions": []}, "old_alpha": {"enabled": true, "disabledVersions": []}, "old_beta": {"enabled": true, "disabledVersions": []}}, "loaders": {"fabric": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "forge": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "legacyfabric": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}, "quilt": {"enabled": true, "disabledMinecraftVersions": [], "disabledVersions": []}}, "platforms": {"curseforge": {"modsEnabled": true, "modpacksEnabled": true}, "ftb": {"modpacksEnabled": true}, "modpacksch": {"modpacksEnabled": false}, "modrinth": {"modsEnabled": true, "modpacksEnabled": true}, "technic": {"modpacksEnabled": true}}, "errorReporting": {"enabled": true, "ignoredMessages": []}}