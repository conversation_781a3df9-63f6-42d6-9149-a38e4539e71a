# suppress inspection "UnusedProperty" for whole file
#
# ATLauncher - https://github.com/ATLauncher/ATLauncher
# Copyright (C) 2013-2020 ATLauncher
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.
#

# This inherits from
# ./ATLauncherLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLightLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLaf.properties

#---- globals ----
@background=$secondary.200
@foreground=$black

#---- override base variables ----
yellow=#b7791f
buttonBackground=$secondary.100
buttonBorder=$secondary.300

border=$secondary.300
focusedBorder=$secondary.400

checkboxBackground=$white
checkboxCheck=$primary.500

separatorColor=$secondary.400

tabActive=$secondary.300

Mods.modSelectionColor=$secondary.300
