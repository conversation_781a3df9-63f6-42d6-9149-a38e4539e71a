[{"id": 108, "title": "FTB Packs Available Again", "content": "Previously we had to remove FTB packs from the launcher at the request of the old FTB CEO <PERSON><PERSON><PERSON>. \r<br/>\r<br/>Recently FTB have changed CEO and the new CEO has once again allowed us to list FTB packs on the launcher.\r<br/>\r<br/>This is great news and we've released an update to add FTB packs back again through the FTB Packs tab.\r<br/>\r<br/>If you had previous instances from before we had to remove the functionality, they should show updates and allow reinstallation again.\r<br/>\r<br/>If you face any issues, please visit <a href=\"https://atl.pw/discord\">our Discord</a>.", "created_at": "2024-11-15T12:55:33.000000Z"}, {"id": 107, "title": "MIcrosoft Account Login <PERSON>", "content": "Currently there are known issues with logging into Microsoft accounts. <a href=\"https://downdetector.com/status/minecraft/?nogeo=true\">Check Downdetector for user reports of the issue</a>.\r<br/>\r<br/>This issue is intermittent over the last week, and affects all launchers and websites that use Minecraft services, including the vanilla launcher and minecraft.net.\r<br/>\r<br/>This issue is at Microsoft/Mojang's end and is not something that ATLauncher or individuals themselves encountering issues can fix.\r<br/>\r<br/>Some people have had luck using a VPN set to another country in order to login, but other than that all that you can do if you face login issues is to keep trying, logins start and stop working constantly, so just keep trying.\r<br/>\r<br/>Again, please note that ATLauncher staff cannot do anything about this, please have some patience and just keep trying the above. Our support cannot do anything to fix this and will just direct you to the above.\r<br/>\r<br/>Thanks", "created_at": "2024-06-10T07:40:57.000000Z"}, {"id": 106, "title": "Serialization Is Bad - RCE in 1.7/1.12", "content": "Unfortunately there is another exploit going around the modded Minecraft community at the moment.\r<br/>\r<br/>This is an RCE which allows bad actors to run code remotely on your computer when connected on the same server as them.\r<br/>\r<br/>The scope at the moment <b>seems to mostly affect mods on 1.7 and 1.12</b> but may also occur on other Minecraft versions, there is still work going on to find a full list of mods and affected versions. If you play singleplayer only you don't have anything to worry about, only if you play online on public servers.\r<br/>\r<br/>Due to the nature of this being a remote code execution exploit, any attacker on the same server as you could've run any code on your computer, so as such there's no way to tell if you've been affected or what code was run.\r<br/>\r<br/>The best course of action currently if you play older modpacks on 1.7/1.12 is to simply not join any public servers unprotected and to add <a href=\"https://modrinth.com/mod/serializationisbad\">this mod</a> to your instances to protect yourself from the exploit.\r<br/>\r<br/>More information about the exploit and affected mods and solutions can be found at <a href=\"https://github.com/dogboy21/serializationisbad\">https://github.com/dogboy21/serializationisbad</a>.\r<br/>\r<br/>There is a thread in <a href=\"https://discord.com/channels/117047818136322057/1134986884796391584\">our Discord</a> if you have any questions.\r<br/>\r<br/><b>NOTE</b>: We posted this on our Discord when it happened, but forgot to post to our News feed, so if you already got the ping from our Discord about this issue, this is the same issue, not a new one.", "created_at": "2023-08-04T13:35:54.000000Z"}, {"id": 105, "title": "Update regarding malware incident", "content": "The situation seems to have relaxed a bit, the details on the malware and what it does is known (again if you're interested take a look <a href=\"https://github.com/fractureiser-investigation/fractureiser\">at this GitHub repo</a>) and we have tools available to detect if you're infected:\r<br/>\r<br/>- <a href=\"https://support.curseforge.com/en/support/solutions/articles/**********-june-2023-infected-mods-detection-tool/\">CurseForge Infected mods detection tool</a>\r<br/>- <a href=\"https://github.com/MCRcortex/nekodetector/releases\">NekoDetector</a>\r<br/>\r<br/>Both these tools will detect for any traces of the malware on your PC or within your modded Minecraft installations (be sure to scan your entire drives, or at least your user folder and any folders where you run Minecraft from).\r<br/>\r<br/>Several platforms have come out and confirmed that they've scanned and removed all traces (if there were any) of the malware from their sites:\r<br/>\r<br/>- <b>ATLauncher</b>: All clear to install and use ATLauncher modpacks\r<br/>- <b>CurseForge</b>: All clear to install and use CurseForge modpacks and install and use CurseForge mods\r<br/>- <b>Modrinth</b>: All clear to install and use Modrinth modpacks and install and use Modrinth mods\r<br/>- <b>Technic</b>: All clear to install and use Technic official modpacks. Non official modpacks were not verified as they use external download sources, so if you plan on installing a third party pack from Technic, you should be cautious and scan after downloading.\r<br/>\r<br/><b>Make sure that you have scanned your system using the tools mentioned above before installing or playing Minecraft.</b>\r<br/>\r<br/>ATLauncher will be investigating how we can detect for and warn against this type of attack going forward, including checking not only for this specific type of malware, but also checking for changes to your mods since downloading. Keep an eye out for more updates on that in the coming week or two.\r<br/>\r<br/>As always, if you have any concerns or questions, please reach out in #malware-incident channel in <a href=\"https://atl.pw/discord\">our Discord</a>.", "created_at": "2023-06-09T02:56:15.000000Z"}, {"id": 104, "title": "Minecraft Modding Malware Incident", "content": "There is currently an ongoing incident within the Minecraft modding community where malware has been found to be included in some Minecraft mods (and bukkit plugins) on CurseForge. This included being included in some modpacks.\r<br/>\r<br/>There are some smart people going through and finding out what the malware does, but at a high level it looks like it steals authentication credentials for Minecraft/Microsoft accounts that are logged into various launchers (not including ATLauncher), as well as stealing browser cookies and discord tokens as well as other things.\r<br/>\r<br/>It self replicates itself, injecting a payload into any Minecraft mods in your system so that it can spread, and that's believed to be how it got onto CurseForge, unknowingly uploaded after the devs machines were infected.\r<br/>\r<br/>Due to the fact that it can replicate itself into mods, it's highly likely that anyone who has been infected with the malware now has it injected into the mods on their system.\r<br/>\r<br/>You can check if you're infected by using the detector tools released by CurseForge <a href=\"https://support.curseforge.com/en/support/solutions/articles/**********-june-2023-infected-mods-detection-tool/\">here</a>.\r<br/>\r<br/>The situation is still very much ongoing, and we'll be looking to place mitigations in place within the launcher to check for the presence of these infected mods as well as at the system level if you're infected. Will be looking to roll that out soon, again some smart people are looking into mitigations and what can be done to detect and remove the malware.\r<br/>\r<br/><b>While more information is still being found out, for now the recommendation is to still not download any mods from anywhere which includes CurseForge, Modrinth and from any other sources, including friends.</b>\r<br/>\r<br/>If you have any questions or more information, please visit us in <a href=\"https://atl.pw/discord\">Discord</a> in the #malware-incident channel.", "created_at": "2023-06-08T00:05:19.000000Z"}]