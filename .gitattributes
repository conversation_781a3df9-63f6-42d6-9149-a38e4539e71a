# Handle line endings automatically for files detected as text
# and leave all files detected as binary untouched.
* text=auto

#
# The above will handle all files NOT found below
#
# These files are text and should be normalized (Convert crlf => lf)
*.css           text
*.df            text
*.htm           text
*.html          text
*.java          text
*.js            text
*.json          text
*.jsp           text
*.jspf          text
*.jspx          text
*.properties    text
*.sh            text
*.tld           text
*.txt           text
*.tag           text
*.tagx          text
*.xml           text

# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.class binary
*.dll   binary
*.ear   binary
*.gif   binary
*.ico   binary
*.jar   binary
*.jpg   binary
*.jpeg  binary
*.png   binary
*.so    binary
*.war   binary

# These are explicitly windows files and should use crlf
*.bat   text eol=crlf

# These are explicitly linux files and should use lf
*.sh   text eol=lf
packaging/aur/atlauncher/atlauncher   text eol=lf
packaging/aur/atlauncher/atlauncher.desktop   text eol=lf
packaging/aur/atlauncher/atlauncher.install   text eol=lf
packaging/aur/atlauncher/PKGBUILD   text eol=lf
packaging/aur/atlauncher-bin/atlauncher   text eol=lf
packaging/aur/atlauncher-bin/atlauncher.desktop   text eol=lf
packaging/aur/atlauncher-bin/atlauncher.install   text eol=lf
packaging/aur/atlauncher-bin/PKGBUILD   text eol=lf
packaging/linux/deb/**   text eol=lf
packaging/linux/rpm/**   text eol=lf
packaging/linux/_common/atlauncher.metainfo.xml   text eol=lf
packaging/linux/_common/atlauncher.desktop   text eol=lf
