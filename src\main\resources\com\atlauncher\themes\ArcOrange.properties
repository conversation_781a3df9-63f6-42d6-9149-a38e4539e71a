# suppress inspection "UnusedProperty" for whole file
#
# ATLauncher - https://github.com/ATLauncher/ATLauncher
# Copyright (C) 2013-2020 ATLauncher
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.
#

# This inherits from
# ./Light.properties
# ./ATLauncherLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLightLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLaf.properties

#---- override base variables ----
@background=#f5f5f5
@foreground=#000000
separatorColor=#9ba2ab
buttonBackground=#ffffff

#---- checkbox ----
CheckBox.icon.focusColor=#f57900
CheckBox.icon.focusedSelectedBorderColor=#f57900
CheckBox.icon.focusedBorderColor=#f57900
CheckBox.icon.background=#ffffff
CheckBox.icon.disabledBackground=#e1e3e6
CheckBox.icon.selectedBackground=#f57900
CheckBox.icon.checkmarkColor=#ffffff
CheckBox.icon.borderColor=#e1e3e6
CheckBox.icon.selectedBorderColor=#f57900

#---- all the things ----
*.selectionBackground=#f57900
*.selectionForeground=#ffffff
*.selectionInactiveBackground=#C36200
*.selectionBackgroundInactive=#c36200
*.background=#f5f5f5
*.focusColor=#f57900
Borders.color=#e1e3e6
Borders.ContrastBorderColor=#E1E3E6
Button.shadowColor=#A6A6A620
Button.startBorderColor=#C4C4C4
Button.endBorderColor=#C4C4C4
Button.focusedBorderColor=#f57900
Button.default.foreground=#000000
Button.default.startBackground=#f57900
Button.default.endBackground=#f57900
Button.default.startBorderColor=#f57900
Button.default.endBorderColor=#f57900
Button.default.shadowColor=#A6A6A650
Button.default.focusedBorderColor=#f57900
ToolBar.background=#F5F5F5
Popup.Toolbar.background=#F5F5F5
Panel.background=#F5F5F5
Panel.foreground=#5c616c
Window.border=1,1,1,1,#5c616c
WelcomeScreen.background=#F5F5F5
WelcomeScreen.Projects.background=#ffffff
MenuBar.foreground=#5c616c
Menu.background=#ffffff
Menu.separatorColor=#F5F5F5
Menu.foreground=#5c616c
MenuItem.foreground=#5c616c
MenuItem.background=#ffffff
PopupMenuSeparator.height=2
Group.separatorColor=#9ba2ab
Tree.background=#ffffff
ProgressBar.background=#f57900
ProgressBar.foreground=#f57900
ProgressBar.progressColor=#f57900
ProgressBar.indeterminateStartColor=#f57900
ProgressBar.indeterminateEndColor=#f57900
Component.focusedBorderColor=#f57900
Component.focusColor=#f57900
Component.focusWidth=1
Component.arc=4
Button.arc=4
SidePanel.background=#e7e8eb
ParameterInfo.background=#fffae3
ParameterInfo.currentOverloadBackground=#fffae3
List.background=#ffffff
List.dropLineColor=#f57900
List.selectionBackground=#f57900
List.selectionForeground=#ffffff
List.selectionInactiveBackground=#C36200
Table.background=#ffffff
Table.selectionBackground=#f57900
Table.selectionForeground=#ffffff
Table.lightSelectionBackground=#f57900
Table.lightSelectionForeground=#ffffff
Table.focusCellBackground=#f57900
Table.focusCellForeground=#ffffff
Table.dropLineColor=#f57900
Table.dropLineShortColor=#f57900
TabbedPane.underlineColor=#f57900
TabbedPane.inactiveUnderlineColor=#f57900
TabbedPane.background=#F5F5F5
Link.hoverForeground=#f57900
Link.activeForeground=#f57900
Link.pressedForeground=#f57900
Link.visitedForeground=#f57900
Link.secondaryForeground=#f57900
Hyperlink.linkColor=#f57900
ComboBox.background=#ffffff
ComboBoxButton.background=#ffffff
ComboBox.ArrowButton.background=#ffffff
TextField.background=#ffffff
TextArea.background=#ffffff
TextPane.background=#ffffff
PasswordField.background=#ffffff
FormattedTextField.background=#ffffff
Editor.background=#f5f5f5
EditorPane.background=#ffffff
CheckBox.background=#F5F5F5
RadioButton.background=#F5F5F5
Slider.background=#F5F5F5
Spinner.background=#F5F5F5
OptionPane.background=#F5F5F5
CompletionPopup.selectionBackground=#F5790055
CompletionPopup.matchForeground=#F57900
CompletionPopup.selectionInactiveBackground=#C36200
Plugins.lightSelectionBackground=#dddee1
Plugins.SearchField.background=#ffffff
Plugins.background=#ffffff
Plugins.Button.installBackground=#f57900
Plugins.Button.installForeground=#ffffff
Plugins.Button.installBorderColor=#f57900
Plugins.Button.installFillBackground=#f57900
Plugins.Button.installFillForeground=#ffffff
Plugins.Button.updateBackground=#f57900
Plugins.Button.updateForeground=#ffffff
Plugins.Button.updateBorderColor=#f57900
Counter.background=#5c616c
Counter.foreground=#ffffff
SearchEverywhere.SearchField.background=#ffffff
SearchEverywhere.Header.background=#F5F5F5
ToolTip.background=#F5F5F5
ToolTip.Actions.background=#F5F5F5
ToolWindow.Header.background=#e7e8eb
ToolWindow.HeaderTab.selectedBackground=#dddee1
ToolWindow.HeaderTab.hoverInactiveBackground=#dddee1
ToolWindow.HeaderTab.selectedInactiveBackground=#dddee1
ToolWindow.Button.selectedBackground=#dddee1
ToolWindow.HeaderTab.underlineColor=#f57900
DefaultTabs.underlineColor=#f57900
DefaultTabs.background=#F5F5F5
EditorTabs.underlineColor=#f57900
EditorTabs.background=#F5F5F5
Notification.background=#F5F5F5
Notification.MoreButton.background=#dddee1
ScrollBar.background=#F5F5F5
