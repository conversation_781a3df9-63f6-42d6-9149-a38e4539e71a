# suppress inspection "UnusedProperty" for whole file
#
# ATLauncher - https://github.com/ATLauncher/ATLauncher
# Copyright (C) 2013-2020 ATLauncher
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.
#

# This inherits from
# ./Dark.properties
# ./ATLauncherLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatDarkLaf.properties
# https://raw.githubusercontent.com/JFormDesigner/FlatLaf/master/flatlaf-core/src/main/resources/com/formdev/flatlaf/FlatLaf.properties

#---- variables ----
red=#e06c75
green=#98c379
yellow=#e5c07b
blue=#61afef
magenta=#c678dd
cyan=#56b6c2
grey=#5c6370

foreground=#dcdfe4
background=#282c34
backgroundDark=#21252b
accent=$green

#---- Base Variables ----
@background=$background
@foreground=$foreground
separatorColor=$background

#---- News ----
News.headerColor=$green
News.linkColor=$blue

#---- Console ----
Console.LogType.debug=$magenta
Console.LogType.info=$green
Console.LogType.warn=$yellow
Console.LogType.error=$red
Console.LogType.default=$foreground

#---- Tabs ----
tabActive=$backgroundDark
TabbedPane.underlineColor=$accent
TabbedPane.inactiveUnderlineColor=$accent
TabbedPane.showTabSeparators=false

#---- Button ----
Button.foreground=$foreground
buttonBackground=$backgroundDark
Button.startBorderColor=$backgroundDark
Button.endBorderColor=$backgroundDark
Button.focusedBorderColor=$backgroundDark
Button.hoverBorderColor=$accent
Button.hoverBackground=$backgroundDark

#---- Checkbox ----
CheckBox.icon.background=$backgroundDark
CheckBox.icon.borderColor=$accent
CheckBox.icon.checkmarkColor=$accent
CheckBox.icon.focusColor=$accent
CheckBox.icon.focusedBorderColor=$accent
CheckBox.icon.selectedBackground=$backgroundDark
CheckBox.icon.selectedBorderColor=$accent
CheckBox.icon.focusedSelectedBorderColor=$accent
CheckBox.icon.disabledBackground=$backgroundDark

#---- Text Field ----
TextField.foreground=$foreground
TextField.background=$backgroundDark
TextField.caretForeground=$foreground
TextField.inactiveForeground=$foreground
TextField.selectionForeground=$foreground
TextField.selectionBackground=$blue

TextField.startBorderColor=$backgroundDark
TextField.endBorderColor=$backgroundDark

#---- Formated Text Field
FormattedTextField.foreground=$foreground
FormattedTextField.background=$backgroundDark
FormattedTextField.caretForeground=$foreground
FormattedTextField.selectionForeground=$foreground
FormattedTextField.selectionBackground=$blue

#---- Password Field ----
PasswordField.foreground=$foreground
PasswordField.background=$backgroundDark
PasswordField.capsLockIconColor=$red
PasswordField.caretForeground=$foreground
PasswordField.inactiveForeground=$foreground
PasswordField.selectionForeground=$foreground
PasswordField.selectionBackground=$blue

#---- Text Area ----
TextArea.foreground=$foreground
TextArea.background=$backgroundDark
TextArea.caretForeground=$foreground
TextArea.inactiveForeground=$foreground
TextArea.selectionForeground=$foreground
TextArea.selectionBackground=$blue

#---- Combo Box ----
ComboBox.foreground=$foreground
ComboBox.background=$backgroundDark
ComboBox.buttonBackground=$backgroundDark
ComboBox.selectionBackground=$background
ComboBox.selectionForeground=$accent
ComboPopup.border=$backgroundDark

#---- Spinner ----
Spinner.foreground=$foreground
Spinner.background=$backgroundDark
Spinner.selectionForeground=$foreground
Spinner.buttonBackground=$backgroundDark

#---- ProgressBar ----
ProgressBar.foreground=$accent
ProgressBar.background=$background

#---- Toaster ----
Toaster.bgColor=$background
Toaster.msgColor=$foreground
Toaster.borderColor=$background

#---- CollapsiblePanel ----
CollapsiblePanel.normal=$foreground
CollapsiblePanel.warning=$yellow
CollapsiblePanel.error=$red

#---- Component
Component.borderColor=$backgroundDark
Component.focusedBorderColor=$accent
Component.disabledBorderColor=$grey
# Component.hoverBorderColor=$accent
Component.iconColor=$foreground
Component.hoverIconColor=$foreground
Component.infoForeground=$accent

#---- EditorPane ----
EditorPane.disabledBackground=$background
EditorPane.inactiveBackground=$background

#---- Mods ----
Mods.modSelectionColor=$backgroundDark
