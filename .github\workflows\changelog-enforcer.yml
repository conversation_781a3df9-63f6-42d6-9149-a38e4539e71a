name: "Pull Request Changelog Enforcer"
on:
  pull_request:
    # The specific activity types are listed here to include "labeled" and "unlabeled"
    # (which are not included by default for the "pull_request" trigger).
    # This is needed to allow skipping enforcement of the changelog in PRs with specific labels,
    # as defined in the (optional) "skipLabels" property.
    types: [opened, synchronize, reopened, ready_for_review, labeled, unlabeled]

jobs:
  # Enforces the update of a changelog file on every pull request
  changelog:
    runs-on: ubuntu-latest
    steps:
    - uses: dangoslen/changelog-enforcer@v3
      with:
        changeLogPath: 'CHANGELOG.md'
        missingUpdateErrorMessage: 'Please ensure you add a line in CHANGELOG.md (matching existing format) detailing your change in ~100 characters or less.'
