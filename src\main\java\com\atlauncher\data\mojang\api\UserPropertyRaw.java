/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.data.mojang.api;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import com.atlauncher.Gsons;
import com.atlauncher.annot.Json;

import net.iharder.Base64;

@Json
public class UserPropertyRaw {
    private String name;
    private String value;
    private String signature;

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    public String getSignature() {
        return this.signature;
    }

    public UserProperty parse() throws IOException {
        return Gsons.DEFAULT.fromJson(new String(Base64.decode(this.value), StandardCharsets.UTF_8),
                UserProperty.class);
    }
}
