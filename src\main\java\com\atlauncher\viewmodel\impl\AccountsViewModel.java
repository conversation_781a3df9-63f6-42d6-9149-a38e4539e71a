/*
 * ATLauncher - https://github.com/ATLauncher/ATLauncher
 * Copyright (C) 2013-2022 ATLauncher
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package com.atlauncher.viewmodel.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import com.atlauncher.data.AbstractAccount;
import com.atlauncher.data.ElyByAccount;
import com.atlauncher.data.MicrosoftAccount;
import com.atlauncher.gui.dialogs.ChangeSkinDialog;
import com.atlauncher.managers.AccountManager;
import com.atlauncher.network.Analytics;
import com.atlauncher.network.analytics.AnalyticsEvent;
import com.atlauncher.viewmodel.base.IAccountsViewModel;

public class AccountsViewModel implements IAccountsViewModel {
    @Override
    public int accountCount() {
        return getAllAccounts().size();
    }

    private List<AbstractAccount> getAllAccounts() {
        List<AbstractAccount> allAccounts = new ArrayList<>();
        allAccounts.addAll(AccountManager.getAccounts());
        allAccounts.addAll(AccountManager.getElyByAccounts());
        return allAccounts;
    }

    private Consumer<List<String>> _onAccountsChanged;

    @Override
    public void onAccountsNamesChanged(Consumer<List<String>> onAccountsChanged) {
        _onAccountsChanged = onAccountsChanged;
        pushNewAccounts();
    }

    /**
     * Update the UI with new accounts
     */
    @Override
    public void pushNewAccounts() {
        _onAccountsChanged.accept(
                getAllAccounts().stream()
                        .map(account -> account.minecraftUsername + " (" + account.getUserType() + ")")
                        .collect(Collectors.toList()));
    }

    private Consumer<AbstractAccount> selected;
    private int selectedAccountIndex = -1;

    @Override
    public void onAccountSelected(Consumer<AbstractAccount> onAccountSelected) {
        selected = onAccountSelected;
    }

    @Override
    public void setSelectedAccount(int index) {
        selectedAccountIndex = index - 1;
        if (index == 0) {
            selected.accept(null);
        } else {
            selected.accept(getSelectedAccount());
        }
    }

    @Nullable
    @Override
    public AbstractAccount getSelectedAccount() {
        List<AbstractAccount> allAccounts = getAllAccounts();
        if (selectedAccountIndex >= 0 && selectedAccountIndex < allAccounts.size()) {
            return allAccounts.get(selectedAccountIndex);
        }
        return null;
    }

    @Override
    public int getSelectedIndex() {
        return selectedAccountIndex + 1;
    }

    @Override
    public boolean refreshAccessToken() {
        Analytics.trackEvent(AnalyticsEvent.simpleEvent("account_refresh_access_token"));

        AbstractAccount account = getSelectedAccount();
        if (account == null) {
            return false;
        }

        boolean success = false;
        if (account instanceof MicrosoftAccount) {
            MicrosoftAccount msAccount = (MicrosoftAccount) account;
            success = msAccount.refreshAccessToken(true);
            if (!success) {
                msAccount.mustLogin = true;
            }
            AccountManager.saveAccounts();
        } else if (account instanceof ElyByAccount) {
            ElyByAccount elyByAccount = (ElyByAccount) account;
            success = elyByAccount.refreshAccessToken(true);
            if (!success) {
                elyByAccount.mustLogin = true;
            }
            AccountManager.saveElyByAccounts();
        }

        return success;
    }

    @Override
    public void updateUsername() {
        AbstractAccount account = getSelectedAccount();
        if (account != null) {
            Analytics.trackEvent(AnalyticsEvent.simpleEvent("account_update_username"));
            account.updateUsername();
            if (account instanceof MicrosoftAccount) {
                AccountManager.saveAccounts();
            } else if (account instanceof ElyByAccount) {
                AccountManager.saveElyByAccounts();
            }
            pushNewAccounts();
        }
    }

    @Override
    public void changeSkin() {
        AbstractAccount account = getSelectedAccount();

        if (account instanceof MicrosoftAccount) {
            ChangeSkinDialog changeSkinDialog = new ChangeSkinDialog((MicrosoftAccount) account);
            changeSkinDialog.setVisible(true);
        }
        // Note: Ely.by skin changing would need a different implementation
    }

    @Override
    public void updateSkin() {
        AbstractAccount account = getSelectedAccount();
        if (account != null) {
            Analytics.trackEvent(AnalyticsEvent.simpleEvent("account_update_skin"));
            account.updateSkin();
        }
    }

    @Override
    public void deleteAccount() {
        AbstractAccount account = getSelectedAccount();
        if (account != null) {
            Analytics.trackEvent(AnalyticsEvent.simpleEvent("account_delete"));
            if (account instanceof MicrosoftAccount) {
                AccountManager.removeAccount((MicrosoftAccount) account);
            } else if (account instanceof ElyByAccount) {
                AccountManager.removeElyByAccount((ElyByAccount) account);
            }
            pushNewAccounts();
        }
    }
}
